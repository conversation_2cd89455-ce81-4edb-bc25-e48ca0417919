/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.MonitorTool;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.StatFs;

import androidx.annotation.Nullable;

import com.thundercomm.MonitorTool.utils.Logger;

import java.io.File;
import java.util.ArrayList;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


/**
 * Memory control service
 * Monitor specified directories and delete oldest files when storage space is insufficient
 */
public class MemoryControlService extends Service {
    private static final String TAG = "MemoryControlService";

    // Constants
    private static final long DEFAULT_MIN_STORAGE_MB = 500;
    private static final long DEFAULT_CHECK_INTERVAL_SECONDS = 30;
    private static final long DEFAULT_MAX_DELETE_SIZE_MB = 100;

    // Current configuration
    private long mMinStorageMB;
    private long mCheckIntervalSeconds;
    private long mMaxDeleteSizeMB; // Maximum file size to delete at once (MB)
    private List<String> mMonitorDirs = new ArrayList<>();

    // Scheduler
    private ScheduledExecutorService mScheduler;
    private Handler mMainHandler;

    // Listener callback for UI display
    private StorageMonitorListener mListener;

    // Statistics
    private int mTotalFilesDeleted = 0;
    private long mTotalSpaceFreed = 0;
    private long mLastCheckTime = 0;

    // Binder instance
    private final IBinder mBinder = new LocalBinder();

    /**
     * Local Binder class for client binding
     */
    public class LocalBinder extends Binder {
        public MemoryControlService getService() {
            return MemoryControlService.this;
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.i(TAG, "MemoryControlService onCreate - Service created");

        mMainHandler = new Handler(Looper.getMainLooper());
        Logger.d(TAG, "Main thread Handler created");

        // Check storage permissions
        checkStoragePermissions();

        // Load local configuration
        Logger.i(TAG, "Loading local configuration...");
        loadLocalConfig();
        checkMonitorDirs();

        // Start scheduler
        Logger.i(TAG, "Starting scheduler...");
        startScheduler();

        Logger.i(TAG, "Service initialization completed");
    }

    /**
     * Get default minimum storage MB
     * @return Default minimum storage MB
     */
    private static long getDefaultMinStorageMB() {
        return DEFAULT_MIN_STORAGE_MB;
    }

    /**
     * Get default check interval seconds
     * @return Default check interval seconds
     */
    private static long getDefaultCheckIntervalSeconds() {
        return DEFAULT_CHECK_INTERVAL_SECONDS;
    }

    /**
     * Get default maximum delete size MB
     * @return Default maximum delete size MB
     */
    private static long getDefaultMaxDeleteSizeMB() {
        return DEFAULT_MAX_DELETE_SIZE_MB;
    }

    /**
     * Get default directory names
     * @return Default directory names
     */
    private static String[] getDefaultDirNames() {
        return new String[]{"DCIM", "Pictures", "Movies", "Download"};
    }

    /**
     * Update scheduler
     */
    private void updateScheduler() {
        Logger.d(TAG, "Updating scheduler configuration, check interval=" + mCheckIntervalSeconds + " seconds");

        // If scheduler already exists, shut it down
        if (mScheduler != null && !mScheduler.isShutdown()) {
            Logger.d(TAG, "Shutting down existing scheduler");
            mScheduler.shutdown();
        }

        // Recreate and start scheduler
        startScheduler();
    }

    /**
     * Check monitor directories
     */
    private void checkMonitorDirs() {
        // Check if directories exist and ensure access permissions
        for (String dirPath : mMonitorDirs) {
            File dir = new File(dirPath);
            if (!dir.exists()) {
                Logger.w(TAG, "Monitor directory does not exist: " + dirPath + ", attempting to create");
                try {
                    boolean created = dir.mkdirs();
                    if (created) {
                        Logger.i(TAG, "Successfully created monitor directory: " + dirPath);
                    } else {
                        Logger.e(TAG, "Could not create monitor directory: " + dirPath);
                    }
                } catch (Exception e) {
                    Logger.e(TAG, "Error creating directory: " + dirPath + ", error: " + e.getMessage(), e);
                }
            } else if (!dir.isDirectory()) {
                Logger.w(TAG, "Monitor path is not a directory: " + dirPath);
            } else {
                if (dir.canRead()) {
                    Logger.d(TAG, "Monitor directory normal and readable: " + dirPath);
                } else {
                    Logger.w(TAG, "Monitor directory not readable: " + dirPath + ", may lack permissions");
                }
            }
        }
    }

    /**
     * Check storage permissions
     */
    private void checkStoragePermissions() {
        Logger.d(TAG, "Checking storage permissions");

        // Check if external storage is available
        String state = Environment.getExternalStorageState();
        if (!Environment.MEDIA_MOUNTED.equals(state)) {
            Logger.e(TAG, "External storage not available, state: " + state);
            return;
        }

        // Check external storage directory
        File externalDir = Environment.getExternalStorageDirectory();
        Logger.i(TAG, "External storage directory: " + externalDir.getAbsolutePath());

        if (!externalDir.exists()) {
            Logger.e(TAG, "External storage directory does not exist");
            return;
        }

        if (!externalDir.canRead()) {
            Logger.e(TAG, "External storage directory not readable");
            return;
        }

        if (!externalDir.canWrite()) {
            Logger.e(TAG, "External storage directory not writable");
            return;
        }

        // Check extended SD card paths
        String[] extSdCardPaths = {
            "/storage/ext_sdcard",
            "/mnt/ext_sdcard",
            "/sdcard/ext_sdcard",
            "/ext_sdcard"
        };

        for (String path : extSdCardPaths) {
            File extSdDir = new File(path);
            if (extSdDir.exists()) {
                Logger.i(TAG, "Found extended SD card path: " + path);
                if (extSdDir.canRead()) {
                    Logger.i(TAG, "Extended SD card readable");
                } else {
                    Logger.w(TAG, "Extended SD card not readable");
                }

                if (extSdDir.canWrite()) {
                    Logger.i(TAG, "Extended SD card writable");
                } else {
                    Logger.w(TAG, "Extended SD card not writable");
                }

                // Check subdirectories
                String[] subDirs = {"DCIM", "Send", "Logs"};
                for (String subDir : subDirs) {
                    File dir = new File(extSdDir, subDir);
                    if (dir.exists()) {
                        Logger.i(TAG, "Subdirectory exists: " + dir.getAbsolutePath());
                    } else {
                        Logger.w(TAG, "Subdirectory does not exist: " + dir.getAbsolutePath() + ", attempting to create");
                        try {
                            boolean created = dir.mkdirs();
                            Logger.i(TAG, "Create directory result: " + (created ? "Success" : "Failed"));
                        } catch (Exception e) {
                            Logger.e(TAG, "Failed to create directory: " + e.getMessage());
                        }
                    }
                }
            }
        }

        Logger.i(TAG, "Storage permissions check completed");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.i(TAG, "MemoryControlService onStartCommand - Service start command received, startId=" + startId);

        // If there is intent data, it can be processed
        if (intent != null) {
            if (intent.hasExtra("reload_config")) {
                Logger.i(TAG, "Received request to reload configuration");
                Logger.i(TAG, "Reloading local configuration");
                loadLocalConfig();
                checkMonitorDirs();
                updateScheduler();
            }

            if (intent.hasExtra("check_now")) {
                Logger.i(TAG, "Received request to check storage immediately");
                mMainHandler.post(this::checkStorage);
            }
        }

        return START_STICKY; // Service will be restarted automatically after being killed
    }

    @Override
    public void onDestroy() {
        Logger.i(TAG, "MemoryControlService onDestroy - Service destroyed");

        // Stop scheduler
        if (mScheduler != null && !mScheduler.isShutdown()) {
            try {
                mScheduler.shutdown();
                Logger.i(TAG, "Scheduler stopped");
            } catch (Exception e) {
                Logger.e(TAG, "Error stopping scheduler: " + e.getMessage(), e);
            }
        }

        super.onDestroy();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Logger.d(TAG, "MemoryControlService onBind - Service bound: " + intent);
        return mBinder;
    }

    /**
     * Start scheduler to periodically check storage space
     */
    private void startScheduler() {
        if (mScheduler != null && !mScheduler.isShutdown()) {
            Logger.d(TAG, "Closing old scheduler");
            mScheduler.shutdown();
        }

        Logger.d(TAG, "Creating new scheduler, check interval: " + mCheckIntervalSeconds + " seconds");
        mScheduler = Executors.newSingleThreadScheduledExecutor();
        mScheduler.scheduleAtFixedRate(this::checkStorage, 0, mCheckIntervalSeconds, TimeUnit.SECONDS);

        Logger.i(TAG, "Storage space check scheduler started, check interval: " + mCheckIntervalSeconds + " seconds");
    }

    /**
     * Check storage space
     */
    private void checkStorage() {
        long currentTime = System.currentTimeMillis();
        Logger.d(TAG, "Starting storage space check, time since last check: " + (currentTime - mLastCheckTime) / 1000 + " seconds");
        mLastCheckTime = currentTime;

        try {
            long availableSpace = getAvailableStorageSpace();
            long availableSpaceMB = availableSpace / (1024 * 1024);

            Logger.i(TAG, "Current available storage space: " + availableSpaceMB + "MB, minimum threshold: " + mMinStorageMB + "MB");

            // Notify UI to update
            notifyStorageStatus(availableSpaceMB);

            // If available space is less than threshold, start cleaning files
            if (availableSpaceMB < mMinStorageMB) {
                Logger.w(TAG, "Storage space insufficient, starting to clean old files");
                cleanOldFiles();

                // Re-check space after cleaning
                long newAvailableSpace = getAvailableStorageSpace();
                long newAvailableSpaceMB = newAvailableSpace / (1024 * 1024);
                Logger.i(TAG, "Available storage space after cleaning: " + newAvailableSpaceMB + "MB (increased by " + (newAvailableSpaceMB - availableSpaceMB) + "MB)");
            } else {
                Logger.d(TAG, "Storage space sufficient, no cleaning needed");
            }
        } catch (Exception e) {
            Logger.e(TAG, "Error checking storage space: " + e.getMessage(), e);
        }
    }

    /**
     * Get available storage space (bytes)
     */
    private long getAvailableStorageSpace() {
        try {
            // Try multiple possible extended SD card paths
            String[] possibleExtSdPaths = {
                "/storage/ext_sdcard",
                "/mnt/ext_sdcard",
                "/sdcard/ext_sdcard",
                "/ext_sdcard"
            };

            for (String extSdCardPath : possibleExtSdPaths) {
                File extSdCardDir = new File(extSdCardPath);
                if (extSdCardDir.exists() && extSdCardDir.canRead()) {
                    Logger.i(TAG, "Found and using extended SD card path: " + extSdCardPath);
                    try {
                        StatFs stat = new StatFs(extSdCardPath);
                        long availableBlocks = stat.getAvailableBlocksLong();
                        long blockSize = stat.getBlockSizeLong();
                        long availableSpace = availableBlocks * blockSize;
                        Logger.v(TAG, "Extended SD card storage space details: Available blocks=" + availableBlocks + ", Block size=" + blockSize + " bytes, Total available=" + availableSpace + " bytes");
                        return availableSpace;
                    } catch (Exception e) {
                        Logger.w(TAG, "Could not get extended SD card space information: " + e.getMessage());
                    }
                }
            }

            Logger.w(TAG, "No available extended SD card path found, attempting to use external storage path");

            // Use standard external storage path, ensure path is valid
            File externalDir = Environment.getExternalStorageDirectory();
            if (externalDir == null || !externalDir.exists()) {
                Logger.e(TAG, "External storage directory does not exist or is invalid");
                throw new IllegalStateException("External storage directory does not exist or is invalid");
            }

            String storagePath = externalDir.getAbsolutePath(); // Use absolute path instead of getPath()
            Logger.d(TAG, "Using external storage path: " + storagePath);

            // Check if path is valid and accessible
            File storageDir = new File(storagePath);
            if (!storageDir.exists()) {
                Logger.e(TAG, "External storage path does not exist: " + storagePath);
                throw new IllegalStateException("External storage path does not exist: " + storagePath);
            }

            if (!storageDir.canRead()) {
                Logger.e(TAG, "External storage path not readable: " + storagePath);
                throw new IllegalStateException("External storage path not readable: " + storagePath);
            }

            StatFs stat = new StatFs(storagePath);
            long availableBlocks = stat.getAvailableBlocksLong();
            long blockSize = stat.getBlockSizeLong();
            long availableSpace = availableBlocks * blockSize;

            Logger.v(TAG, "Storage space details: Available blocks=" + availableBlocks + ", Block size=" + blockSize + " bytes, Total available=" + availableSpace + " bytes");
            return availableSpace;
        } catch (Exception e) {
            Logger.e(TAG, "Failed to get external storage space: " + e.getMessage(), e);
            try {
                // Try to use application internal storage
                Logger.w(TAG, "Attempting to use application internal storage path");
                File internalDir = getFilesDir();
                if (internalDir != null && internalDir.exists() && internalDir.canRead()) {
                    StatFs stat = new StatFs(internalDir.getAbsolutePath());
                    long availableBlocks = stat.getAvailableBlocksLong();
                    long blockSize = stat.getBlockSizeLong();
                    long availableSpace = availableBlocks * blockSize;
                    Logger.d(TAG, "Internal storage path: " + internalDir.getAbsolutePath() + ", Available space: " + (availableSpace / (1024 * 1024)) + "MB");
                    return availableSpace;
                } else {
                    Logger.e(TAG, "Internal storage path is invalid or inaccessible");
                    return 0;
                }
            } catch (Exception e2) {
                Logger.e(TAG, "Failed to get internal storage space as well: " + e2.getMessage(), e2);
                return 0;
            }
        }
    }

    /**
     * Clean old files
     */
    private void cleanOldFiles() {
        Logger.i(TAG, "Starting to clean old files, number of monitor directories: " + mMonitorDirs.size());

        int filesDeleted = 0;
        long spaceFreed = 0;
        long maxDeleteBytes = mMaxDeleteSizeMB * 1024 * 1024; // Convert to bytes

        // Create a unified file list for cross-directory comparison
        List<File> allFiles = new ArrayList<>();

        // Collect files from all directories
        for (String dirPath : mMonitorDirs) {
            if (dirPath == null || dirPath.trim().isEmpty()) {
                Logger.w(TAG, "Skipping empty directory path");
                continue;
            }

            File dir = new File(dirPath);
            if (!dir.exists() || !dir.isDirectory()) {
                Logger.w(TAG, "Directory does not exist or is not a directory: " + dirPath);
                if (!dir.exists()) {
                    Logger.i(TAG, "Attempting to create directory: " + dirPath);
                    try {
                        boolean created = dir.mkdirs();
                        if (created) {
                            Logger.i(TAG, "Successfully created directory: " + dirPath);
                        } else {
                            Logger.e(TAG, "Could not create directory: " + dirPath);
                            continue;
                        }
                    } catch (Exception e) {
                        Logger.e(TAG, "Error creating directory: " + dirPath, e);
                        continue;
                    }
                } else {
                    continue;
                }
            }

            if (!dir.canRead()) {
                Logger.w(TAG, "Directory not readable, skipping: " + dirPath);
                continue;
            }

            Logger.i(TAG, "Scanning directory: " + dirPath);

            // Get all files in the directory
            File[] files = dir.listFiles();
            if (files == null) {
                Logger.w(TAG, "Could not list directory contents: " + dirPath);
                continue;
            }

            if (files.length == 0) {
                Logger.d(TAG, "Directory is empty: " + dirPath);
                continue;
            }

            Logger.d(TAG, "Found " + files.length + " files in directory " + dirPath);

            // Add files to the unified list
            for (File file : files) {
                if (file.isFile()) {
                    if (file.canWrite()) {
                        allFiles.add(file);
                    } else {
                        Logger.d(TAG, "Skipping non-writable file: " + file.getAbsolutePath());
                    }
                } else {
                    Logger.d(TAG, "Skipping non-file item: " + file.getName());
                }
            }
        }

        if (allFiles.isEmpty()) {
            Logger.i(TAG, "No files found for deletion");
            return;
        }

        Logger.i(TAG, "Found " + allFiles.size() + " files for cross-directory comparison");

        // Sort by modification time (oldest first)
        Collections.sort(allFiles, Comparator.comparingLong(File::lastModified));

        if (!allFiles.isEmpty()) {
            Logger.d(TAG, "Files sorted by time, oldest file: " + allFiles.get(0).getName() +
                    ", Path: " + allFiles.get(0).getAbsolutePath() +
                    ", Modified time: " + new java.util.Date(allFiles.get(0).lastModified()));
        }

        // Delete oldest files until space is enough or delete size limit is reached
        for (File file : allFiles) {
            if (!file.exists()) {
                Logger.d(TAG, "File does not exist, skipping: " + file.getAbsolutePath());
                continue;
            }

            if (!file.canWrite()) {
                Logger.d(TAG, "File not writable, skipping: " + file.getAbsolutePath());
                continue;
            }

            long fileSize = file.length();
            Logger.d(TAG, "Preparing to delete file: " + file.getName() +
                    ", Path: " + file.getAbsolutePath() +
                    ", Size: " + (fileSize / 1024) + "KB, Modified time: " + new java.util.Date(file.lastModified()));

            try {
                boolean deleted = file.delete();

                if (deleted) {
                    filesDeleted++;
                    spaceFreed += fileSize;
                    mTotalFilesDeleted++;
                    mTotalSpaceFreed += fileSize;

                    Logger.i(TAG, "File deleted: " + file.getAbsolutePath() + ", Size: " + (fileSize / 1024) + "KB");
                    notifyFileDeleted(file.getAbsolutePath(), fileSize);

                    // Check if delete size limit is reached
                    if (spaceFreed >= maxDeleteBytes) {
                        Logger.i(TAG, "Delete size limit " + mMaxDeleteSizeMB + "MB reached, stopping cleanup");
                        break;
                    }
                } else {
                    Logger.w(TAG, "Could not delete file: " + file.getAbsolutePath());
                }
            } catch (Exception e) {
                Logger.e(TAG, "Error deleting file: " + file.getAbsolutePath(), e);
            }

            // Check if space is already sufficient
            try {
                long availableSpaceMB = getAvailableStorageSpace() / (1024 * 1024);
                if (availableSpaceMB >= mMinStorageMB) {
                    Logger.i(TAG, "Storage space sufficient, stopping cleanup");
                    break;
                }
            } catch (Exception e) {
                Logger.e(TAG, "Error checking storage space", e);
                break;
            }
        }

        Logger.i(TAG, "Cleanup completed, deleted " + filesDeleted + " files, freed space " + (spaceFreed / (1024 * 1024)) + "MB");
        Logger.i(TAG, "Total files deleted: " + mTotalFilesDeleted + ", total freed space: " + (mTotalSpaceFreed / (1024 * 1024)) + "MB");
    }

    /**
     * Format configuration value for logging
     * @param value Configuration value
     * @return Formatted string
     */
    private String formatValue(String value) {
        if (value == null) {
            return "null";
        } else if (value.isEmpty()) {
            return "Empty string";
        } else {
            return value;
        }
    }

    /**
     * Load configuration from local resources only
     */
    private void loadLocalConfig() {
        Logger.i(TAG, "Loading configuration from local resources only");

        try {
            // Load configuration from local resources
            mMinStorageMB = getResources().getInteger(R.integer.min_storage_mb);
            mCheckIntervalSeconds = getResources().getInteger(R.integer.check_interval_seconds);
            mMaxDeleteSizeMB = getResources().getInteger(R.integer.max_delete_size_mb);

            Logger.d(TAG, "Loaded local configuration: minStorageMB=" + mMinStorageMB);
            Logger.d(TAG, "Loaded local configuration: checkIntervalSeconds=" + mCheckIntervalSeconds);
            Logger.d(TAG, "Loaded local configuration: maxDeleteSizeMB=" + mMaxDeleteSizeMB);

            // Load monitor directories from local resources
            String[] localDirs = getResources().getStringArray(R.array.monitor_directories);
            mMonitorDirs.clear();

            for (String dir : localDirs) {
                if (dir != null && !dir.trim().isEmpty()) {
                    String dirPath = dir.trim();

                    // Process special paths
                    if (dirPath.startsWith("/ext_sdcard/")) {
                        // Try multiple possible extended SD card paths
                        String[] possiblePaths = {
                            "/storage/ext_sdcard",
                            "/mnt/ext_sdcard",
                            "/sdcard/ext_sdcard"
                        };

                        boolean pathFound = false;
                        for (String basePath : possiblePaths) {
                            File baseDir = new File(basePath);
                            if (baseDir.exists() && baseDir.canRead()) {
                                // Use the found valid path
                                String relativePath = dirPath.substring("/ext_sdcard".length());
                                dirPath = basePath + relativePath;
                                Logger.i(TAG, "Found valid extended SD card path: " + basePath);
                                Logger.d(TAG, "Converted path: " + dir.trim() + " -> " + dirPath);
                                pathFound = true;
                                break;
                            }
                        }

                        if (!pathFound) {
                            Logger.w(TAG, "No valid extended SD card path found, "
                                    + "attempting to use original path: " + dirPath);
                        }
                    } else if (!dirPath.startsWith("/")) {
                        // If it's a relative path, add to external storage directory
                        File externalDir = Environment.getExternalStorageDirectory();
                        dirPath = new File(externalDir, dirPath).getAbsolutePath();
                        Logger.d(TAG, "Converted relative path: " + dir.trim() + " -> " + dirPath);
                    }

                    mMonitorDirs.add(dirPath);
                    Logger.i(TAG, "Added monitor directory: " + dirPath);
                }
            }

            Logger.i(TAG, "Local configuration loaded successfully: Min storage=" + mMinStorageMB
                    + "MB, Check interval=" + mCheckIntervalSeconds + " seconds, "
                    + "Max delete size=" + mMaxDeleteSizeMB + "MB, Number of monitor directories="
                    + mMonitorDirs.size());

        } catch (Exception e) {
            Logger.e(TAG, "Failed to load local configuration: " + e.getMessage(), e);
            // Fall back to default values
            mMinStorageMB = getDefaultMinStorageMB();
            mCheckIntervalSeconds = getDefaultCheckIntervalSeconds();
            mMaxDeleteSizeMB = getDefaultMaxDeleteSizeMB();
            Logger.w(TAG, "Using fallback default configuration");
        }
    }



    /**
     * Set listener
     */
    public void setListener(StorageMonitorListener listener) {
        Logger.d(TAG, "Setting storage monitor listener: " + (listener != null ? "Set" : "Removed"));
        mListener = listener;
    }

    /**
     * Notify storage status
     */
    private void notifyStorageStatus(final long availableSpaceMB) {
        if (mListener != null) {
            Logger.v(TAG, "Notifying UI to update storage status: Available space=" + availableSpaceMB + "MB, Threshold=" + mMinStorageMB + "MB");
            mMainHandler.post(() -> mListener.onStorageStatusUpdated(availableSpaceMB, mMinStorageMB));
        }
    }

    /**
     * Notify file deletion
     */
    private void notifyFileDeleted(final String filePath, final long fileSize) {
        if (mListener != null) {
            Logger.v(TAG, "Notifying UI file deleted: " + filePath + ", Size=" + (fileSize / 1024) + "KB");
            mMainHandler.post(() -> mListener.onFileDeleted(filePath, fileSize));
        }
    }

    /**
     * Get minimum storage threshold (MB)
     */
    public long getMinStorageMB() {
        return mMinStorageMB;
    }

    /**
     * Get check interval (seconds)
     */
    public long getCheckIntervalSeconds() {
        return mCheckIntervalSeconds;
    }

    /**
     * Get maximum delete size (MB)
     */
    public long getMaxDeleteSizeMB() {
        return mMaxDeleteSizeMB;
    }

    /**
     * Storage monitor listener interface
     */
    public interface StorageMonitorListener {
        /**
         * Storage status updated
         * @param availableSpaceMB Available space (MB)
         * @param minStorageMB Minimum threshold (MB)
         */
        void onStorageStatusUpdated(long availableSpaceMB, long minStorageMB);

        /**
         * File deleted
         * @param filePath File path
         * @param fileSize File size (bytes)
         */
        void onFileDeleted(String filePath, long fileSize);
    }
}