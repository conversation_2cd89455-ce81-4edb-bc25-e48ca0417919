/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.MonitorTool.model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AppInfo entity class.
 * Used to store application configuration information, including package name, permission list and auto-start settings.
 */
public class AppInfo {
    private String packageName;
    private String permission;
    private boolean autoBoot;
    private transient List<String> permissionList;

    /**
     * Default constructor.
     */
    public AppInfo() {}

    /**
     * Constructor with parameters.
     * @param packageName Application package name
     * @param permission Permission string (comma separated)
     * @param autoBoot Whether to auto-start
     */
    public AppInfo(String packageName, String permission, boolean autoBoot) {
        this.packageName = packageName;
        this.permission = permission;
        this.autoBoot = autoBoot;
    }

    /**
     * Get application package name.
     * @return Application package name
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * Set application package name.
     * @param packageName Application package name
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * Get permission string.
     * @return Permission string (comma separated)
     */
    public String getPermission() {
        return permission;
    }

    /**
     * Set permission string.
     * @param permission Permission string (comma separated)
     */
    public void setPermission(String permission) {
        this.permission = permission;
        this.permissionList = null; // Reset cache
    }

    /**
     * Get whether to auto-start.
     * @return Whether to auto-start
     */
    public boolean isAutoBoot() {
        return autoBoot;
    }

    /**
     * Set whether to auto-start.
     * @param autoBoot Whether to auto-start
     */
    public void setAutoBoot(boolean autoBoot) {
        this.autoBoot = autoBoot;
    }

    /**
     * Parse permission string to permission list.
     * @return Permission list
     */
    public List<String> getPermissionList() {
        if (permissionList == null && permission != null && !permission.trim().isEmpty()) {
            permissionList = Arrays.asList(permission.split(","))
                .stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
        }
        return permissionList != null ? permissionList : new ArrayList<>();
    }

    /**
     * Check if there are permission configurations.
     * @return Whether there are permission configurations
     */
    public boolean hasPermissions() {
        return !getPermissionList().isEmpty();
    }

    /**
     * Check if contains specified permission.
     * @param permission Permission name
     * @return Whether contains this permission
     */
    public boolean hasPermission(String permission) {
        return getPermissionList().contains(permission);
    }

    @Override
    public String toString() {
        return "AppInfo{" +
               "packageName='" + packageName + '\'' +
               ", autoBoot=" + autoBoot +
               ", permissions=" + getPermissionList().size() +
               "}";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        AppInfo appInfo = (AppInfo) obj;
        return packageName != null ? packageName.equals(appInfo.packageName) : appInfo.packageName == null;
    }

    @Override
    public int hashCode() {
        return packageName != null ? packageName.hashCode() : 0;
    }
}
