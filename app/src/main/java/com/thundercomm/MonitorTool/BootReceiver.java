/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.MonitorTool;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.thundercomm.MonitorTool.utils.Logger;

/**
 * Boot startup receiver
 * Listens for system boot completed broadcast, automatically starts memory control service
 */
public class BootReceiver extends BroadcastReceiver {
    private static final String TAG = "BootReceiver";
    private static final String ACTION_LOCKED_BOOT_COMPLETED = "android.intent.action.LOCKED_BOOT_COMPLETED";

    @Override
    public void onReceive(Context context, Intent intent) {
        Logger.i(TAG, "Broadcast received: " + intent.getAction());

        if (intent.getAction() == null) {
            Logger.w(TAG, "Received broadcast with empty Action, ignoring");
            return;
        }

        // Log device information
        Logger.d(TAG, "Device info: " + Build.MANUFACTURER + " " + Build.MODEL + ", Android " + Build.VERSION.RELEASE + " (API " + Build.VERSION.SDK_INT + ")");

        // Handle boot completed broadcast
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction()) ||
            getLockedBootCompletedAction().equals(intent.getAction())) {

            Logger.i(TAG, "System boot completed, preparing to start services");

            try {
                // Start memory control service
                Intent memoryServiceIntent = new Intent(context, MemoryControlService.class);
                context.startService(memoryServiceIntent);
                Logger.i(TAG, "Memory control service started successfully");

                // Start app monitoring service
                 Intent appMonitorServiceIntent = new Intent(context, MonitorAppService.class);
                 context.startService(appMonitorServiceIntent);
                 Logger.i(TAG, "App monitoring service started successfully");

                // Start settings manager service
                Intent settingsManagerServiceIntent = new Intent(context, SettingsManagerService.class);
                context.startService(settingsManagerServiceIntent);
                Logger.i(TAG, "Settings manager service started successfully");
            } catch (Exception e) {
                Logger.e(TAG, "Failed to start services: " + e.getMessage(), e);
            }
        } else {
            Logger.d(TAG, "Received other broadcast, not processing: " + intent.getAction());
        }
    }

    /**
     * Get locked boot completed action
     * @return Locked boot completed action
     */
    private static String getLockedBootCompletedAction() {
        return ACTION_LOCKED_BOOT_COMPLETED;
    }
}