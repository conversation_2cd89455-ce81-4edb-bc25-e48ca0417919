/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.MonitorTool;

import android.app.AppOpsManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.database.ContentObserver;
import android.os.Handler;
import android.os.IBinder;
import android.os.IDeviceIdleController;
import android.os.Looper;
import android.os.PowerManager;
import android.os.ServiceManager;
import android.provider.Settings;
import android.telephony.TelephonyManager;

import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.thundercomm.MonitorTool.model.AppInfo;
import com.thundercomm.MonitorTool.utils.ConfigKeys;
import com.thundercomm.MonitorTool.utils.ConfigToolServiceHelper;
import com.thundercomm.MonitorTool.utils.ConfigToolServiceHelper.ConfigToolServiceListener;
import com.thundercomm.MonitorTool.utils.Logger;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Settings Manager Service.
 * Manages all application settings and configurations.
 */
public class SettingsManagerService extends Service {
    private static final String TAG = "SettingsManagerService";

    // Special permissions mapping that need to be handled through AppOpsManager
    private static final Map<String, String> APPOPS_PERMISSIONS = new ConcurrentHashMap<String, String>() {{
        put("android.permission.MANAGE_EXTERNAL_STORAGE", AppOpsManager.OPSTR_MANAGE_EXTERNAL_STORAGE);
        put("android.permission.SYSTEM_ALERT_WINDOW", AppOpsManager.OPSTR_SYSTEM_ALERT_WINDOW);
        put("android.permission.WRITE_SETTINGS", AppOpsManager.OPSTR_WRITE_SETTINGS);
        put("android.permission.REQUEST_INSTALL_PACKAGES", AppOpsManager.OPSTR_REQUEST_INSTALL_PACKAGES);
        put("android.permission.ACCESS_NOTIFICATIONS", AppOpsManager.OPSTR_ACCESS_NOTIFICATIONS);
        // More special permissions can be added as needed
    }};

    // Retry related constants
    private static final int MAX_RETRY_COUNT = 10;
    private static final long RETRY_INTERVAL_MS = 5000; // 5 seconds

    // SIM operation timing constants
    private static final long SIM_POWER_DOWN_DELAY_MS = 1000; // 1 second
    private static final long SIM_SWITCH_DELAY_MS = 500; // 0.5 seconds

    // SIM card related constants
    private static final String SIM_SWITCH_PATH = "/sys/devices/platform/soc/soc:gpio_info/sim_switch_gpio122";
    private static final String SIM_TYPE_ESIM = "esim";
    private static final String SIM_TYPE_USIM = "usim";

    // Impact Detection related constants
    private static final String KEY_COLLISION_ENABLED = "collision_enabled";

    // Configuration type enumeration
    private enum ConfigType {
        STRING, BOOLEAN, INTEGER, FLOAT, JSON
    }

    // Broadcast Action constants
    public static final String ACTION_CONFIG_UPDATED = "com.thundercomm.action.CONFIG_UPDATED";
    public static final String EXTRA_CONFIG_KEY = "config_key";

    // Static Map to store all configurations
    private static final Map<String, Object> sConfigMap = new ConcurrentHashMap<>();

    // Configuration type mapping
    private final Map<String, ConfigType> mConfigTypeMap = new HashMap<>();

    private ConfigToolServiceHelper mConfigHelper;
    private ConfigToolServiceHelper.ConfigToolServiceEnhancedListenerAdapter mServiceListener;
    private Set<String> mPendingConfigKeys = new HashSet<>();
    private Handler mHandler;
    private int mRetryCount = 0;
    private Gson mGson;

    // ContentObserver for monitoring Settings.System changes
    private ContentObserver mCollisionEnabledObserver;
    private boolean mIsUpdatingFromConfig = false; // 防止循环更新的标志

    private TelephonyManager mTelephonyManager;
    @Override
    public void onCreate() {
        super.onCreate();
        Logger.i(TAG, "SettingsManagerService onCreate");

        mTelephonyManager = this.getSystemService(TelephonyManager.class);
        // Initialize Handler
        mHandler = new Handler(Looper.getMainLooper());

        // Initialize Gson
        mGson = new Gson();

        // Initialize configuration type mapping
        initConfigTypeMap();

        // Initialize ConfigTool service
        initConfigHelper();

        // Initialize ContentObserver for collision_enabled
        initCollisionEnabledObserver();

        // Register application installation listener
        registerPackageInstallReceiver();
    }

    /**
     * Initialize configuration type mapping.
     * Add each configuration type here.
     */
    private void initConfigTypeMap() {
        // Add configuration keys and corresponding types
        mConfigTypeMap.put(ConfigKeys.SIM_CONFIG, ConfigType.STRING);

        mConfigTypeMap.put(ConfigKeys.AGPS_CONFIG, ConfigType.BOOLEAN);
        mConfigTypeMap.put(ConfigKeys.APP_INFO, ConfigType.JSON);

        // Impact Detection configuration
        mConfigTypeMap.put(ConfigKeys.IMPACT_DETECTION, ConfigType.BOOLEAN);

        // Add more configuration keys and types...
    }

    private void initConfigHelper() {
        mConfigHelper = ConfigToolServiceHelper.getInstance();
        mServiceListener = new ConfigToolServiceHelper.ConfigToolServiceEnhancedListenerAdapter() {
            @Override
            public void onServiceConnected() {
                Logger.i(TAG, "ConfigTool service connected, starting to load configuration");
                // Service connected successfully, start loading configuration
                loadAllConfigurations();
            }

            @Override
            public void onServiceDisconnected() {
                Logger.w(TAG, "ConfigTool service disconnected");
            }

            @Override
            public void onServiceBindFailed() {
                Logger.e(TAG, "ConfigTool service bind failed");
                // Service binding failed, try to retry
                retryBindService();
            }

            @Override
            public void onConfigValuesChanged(Map<String, Object> changedValues) {
                // Handle changed configurations
                Logger.i(TAG, "Configuration values changed: " + changedValues.keySet());
                for (String key : changedValues.keySet()) {
                    Object value = changedValues.get(key);
                    processConfigChange(key, value);
                }
            }
        };
        mConfigHelper.addListener(mServiceListener);

        // Initialize and connect service
        Logger.i(TAG, "Initializing ConfigTool service");
        mConfigHelper.init(this);
    }

    private void retryBindService() {
        if (mRetryCount >= MAX_RETRY_COUNT) {
            Logger.e(TAG, "Failed to bind ConfigTool service after " + MAX_RETRY_COUNT + " retries");
            return;
        }

        mRetryCount++;
        Logger.i(TAG, "Retrying to bind ConfigTool service, attempt " + mRetryCount + "/" + MAX_RETRY_COUNT);
        mHandler.postDelayed(() -> {
            mConfigHelper.init(this);
        }, RETRY_INTERVAL_MS);
    }

    /**
     * Initialize ContentObserver for monitoring Settings.System collision_enabled changes
     */
    private void initCollisionEnabledObserver() {
        mCollisionEnabledObserver = new ContentObserver(mHandler) {
            @Override
            public void onChange(boolean selfChange) {
                if (mIsUpdatingFromConfig) {
                    Logger.d(TAG, "Ignoring Settings.System change triggered by config update");
                    return;
                }

                try {
                    // 重新读取Settings.System中的值
                    int collisionEnabled = Settings.System.getInt(getContentResolver(), KEY_COLLISION_ENABLED, 0);
                    boolean impactDetection = (collisionEnabled == 1);

                    Logger.i(TAG, "collision_enabled changed to: " + collisionEnabled + ", updating config");

                    // 更新本地缓存
                    updateConfig(ConfigKeys.IMPACT_DETECTION, impactDetection);

                    // 保存到ConfigTool
                    saveImpactDetectionToConfig(impactDetection);

                } catch (Exception es) {
                    Logger.e(TAG, "Error handling collision_enabled change: " + es.getMessage(), es);
                }
            }
        };

        try {
            // 注册监听
            getContentResolver().registerContentObserver(
                Settings.System.getUriFor(KEY_COLLISION_ENABLED),
                false,
                mCollisionEnabledObserver
            );
            Logger.i(TAG, "ContentObserver registered for collision_enabled");
        } catch (Exception es) {
            Logger.e(TAG, "Failed to register ContentObserver: " + es.getMessage(), es);
        }
    }

    private void loadAllConfigurations() {
        Logger.i(TAG, "Loading all configurations");

        // Reset retry count and pending configuration list
        mRetryCount = 0;
        mPendingConfigKeys.clear();

        // Define all configuration keys to be retrieved (only need to add new configuration keys here)
        List<String> allConfigKeys = Arrays.asList(
            ConfigKeys.SIM_CONFIG, ConfigKeys.AGPS_CONFIG, ConfigKeys.APP_INFO, ConfigKeys.IMPACT_DETECTION
            // Add more configuration keys...
        );

        // Initialize pending configuration list
        mPendingConfigKeys.addAll(allConfigKeys);

        // Try to get all configurations
        for (String key : allConfigKeys) {
            loadConfig(key);
        }

        // If there are still configurations not successfully retrieved, start retry mechanism
        if (!mPendingConfigKeys.isEmpty()) {
            Logger.i(TAG, "Some configurations not loaded: " + mPendingConfigKeys + ", will retry");
            scheduleRetry();
        } else {
            Logger.i(TAG, "All configurations loaded successfully");
        }
    }

    /**
     * Unified configuration loading.
     */
    private void loadConfig(String key) {
        if (!mConfigTypeMap.containsKey(key)) {
            Logger.w(TAG, "Unknown config key: " + key);
            mPendingConfigKeys.remove(key);
            return;
        }

        try {
            ConfigType type = mConfigTypeMap.get(key);
            Object value = getConfigValue(key, type);

            if (value != null) {
                // Update configuration Map
                updateConfig(key, value);

                // Process configuration
                processConfig(key, value);

                // Remove from pending list
                mPendingConfigKeys.remove(key);
                Logger.i(TAG, "Successfully loaded config: " + key + " = " + value);
            } else {
                Logger.w(TAG, "Failed to load config: " + key);
            }
        } catch (Exception ex) {
            Logger.e(TAG, "Error loading config for key " + key + ": " + ex.getMessage(), ex);
        }
    }

    /**
     * Get configuration value by type.
     */
    private Object getConfigValue(String key, ConfigType type) {
        if (mConfigHelper == null || !mConfigHelper.isServiceConnected()) {
            Logger.w(TAG, "ConfigHelper not available or not connected");
            return null;
        }

        try {
            switch (type) {
                case STRING:
                    String strValue = mConfigHelper.getConfigValue(key, "");
                    if (strValue.isEmpty()) {
                        return null;
                    }
                    // Clean string value, remove possible quotes
                    strValue = cleanStringValue(strValue);
                    Logger.d(TAG, "Got config " + key + " = " + strValue);
                    return strValue;

                case BOOLEAN:
                    return mConfigHelper.getBooleanConfig(key, false);

                case INTEGER:
                    int intValue = mConfigHelper.getIntConfig(key, Integer.MIN_VALUE);
                    return intValue == Integer.MIN_VALUE ? null : intValue;

                case FLOAT:
                    // ConfigToolServiceHelper doesn't have direct getFloat method, use getConfigValue then convert
                    String floatStr = mConfigHelper.getConfigValue(key, "");
                    if (floatStr != null && !floatStr.isEmpty()) {
                        // // Clean string value, remove possible quotes
                        // floatStr = cleanStringValue(floatStr);
                        try {
                            return Float.parseFloat(floatStr);
                        } catch (NumberFormatException ex) {
                            Logger.e(TAG, "Error parsing float value for key " + key + ": " + ex.getMessage(), ex);
                            return null;
                        }
                    }
                    return null;

                case JSON:
                    String jsonStr = mConfigHelper.getConfigValue(key, "");
                    if (jsonStr.isEmpty()) {
                        return null;
                    }
                    try {
                        return JsonParser.parseString(jsonStr);
                    } catch (Exception ex) {
                        Logger.e(TAG, "Error parsing JSON for key " + key + ": " + ex.getMessage(), ex);
                        return null;
                    }

                default:
                    Logger.w(TAG, "Unknown config type: " + type);
                    return null;
            }
        } catch (Exception ex) {
            Logger.e(TAG, "Error getting config value for key " + key + ": " + ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * Clean string value, remove possible quotes.
     */
    private String cleanStringValue(String value) {
        if (value == null) {
            return null;
        }
        // Remove leading and trailing spaces and quotes
        String cleaned = value.trim();
        if (cleaned.startsWith("\"") && cleaned.endsWith("\"")) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }
        return cleaned;
    }

    private void scheduleRetry() {
        if (mRetryCount >= MAX_RETRY_COUNT) {
            Logger.e(TAG, "Failed to load some configurations after " + MAX_RETRY_COUNT + " retries: " + mPendingConfigKeys);
            return;
        }

        mRetryCount++;
        Logger.i(TAG, "Scheduling retry " + mRetryCount + "/" + MAX_RETRY_COUNT + " for pending configs: " + mPendingConfigKeys);

        mHandler.postDelayed(() -> {
            // Copy pending list because it will be modified during processing
            Set<String> keysToRetry = new HashSet<>(mPendingConfigKeys);
            for (String key : keysToRetry) {
                loadConfig(key);
            }

            // If there are still configurations not successfully retrieved, continue retrying
            if (!mPendingConfigKeys.isEmpty()) {
                scheduleRetry();
            } else {
                Logger.i(TAG, "All configurations loaded successfully after retries");
            }
        }, RETRY_INTERVAL_MS);
    }

    /**
     * Handle configuration changes.
     */
    private void processConfigChange(String key, Object value) {
        if (!mConfigTypeMap.containsKey(key)) {
            Logger.w(TAG, "Unknown config key: " + key);
            return;
        }

        try {
            ConfigType type = mConfigTypeMap.get(key);
            Object convertedValue = convertConfigValue(value, type);

            if (convertedValue != null) {
                // Update configuration Map
                updateConfig(key, convertedValue);

                // Process configuration
                processConfig(key, convertedValue);
                Logger.i(TAG, "Successfully processed config change: " + key + " = " + convertedValue);
            } else {
                Logger.w(TAG, "Failed to process config change: " + key);
            }
        } catch (Exception ex) {
            Logger.e(TAG, "Error processing config change for key " + key + ": " + ex.getMessage(), ex);
        }
    }

    /**
     * Convert configuration value to correct type.
     */
    private Object convertConfigValue(Object value, ConfigType type) {
        try {
            if (value == null) {
                return null;
            }

            switch (type) {
                case STRING:
                    return value.toString();

                case BOOLEAN:
                    if (value instanceof Boolean) {
                        return value;
                    } else if (value instanceof String) {
                        return Boolean.parseBoolean((String) value);
                    }
                    break;

                case INTEGER:
                    if (value instanceof Integer) {
                        return value;
                    } else if (value instanceof String) {
                        return Integer.parseInt((String) value);
                    }
                    break;

                case FLOAT:
                    if (value instanceof Float) {
                        return value;
                    } else if (value instanceof String) {
                        return Float.parseFloat((String) value);
                    }
                    break;

                case JSON:
                    if (value instanceof JsonElement) {
                        return value;
                    } else if (value instanceof String) {
                        return JsonParser.parseString((String) value);
                    }
                    break;
            }

            Logger.w(TAG, "Could not convert value " + value + " to type " + type);
            return null;
        } catch (Exception ex) {
            Logger.e(TAG, "Error converting value " + value + " to type " + type + ": " + ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * Update configuration Map.
     */
    private void updateConfig(String key, Object value) {
        sConfigMap.put(key, value);
    }

    /**
     * Process configuration (dispatch to appropriate handling methods based on configuration key).
     * This is where users need to add functionality.
     */
    private void processConfig(String key, Object value) {
        try {
            // Process configuration (only need to add new configuration handling branches here)
            if (ConfigKeys.SIM_CONFIG.equals(key) && value instanceof String) {
                handleSim((String) value);
            }
            else if (ConfigKeys.AGPS_CONFIG.equals(key) && value instanceof Boolean) {
                handleAgps((Boolean) value);
            }
            else if (ConfigKeys.APP_INFO.equals(key) && value instanceof JsonElement) {
                handleAppInfo((JsonElement) value);
            }
            else if (ConfigKeys.IMPACT_DETECTION.equals(key) && value instanceof Boolean) {
                handleImpactDetection((Boolean) value);
            } else {
                // Unknown configuration, use generic processing method
                handleGenericConfig(key, value);
            }
        } catch (Exception ex) {
            Logger.e(TAG, "Error processing config for key " + key + ": " + ex.getMessage(), ex);
        }
    }

    /**
     * Handle SIM configuration.
     * Set SIM switch GPIO according to SIM type.
     */
    private void handleSim(String sim) {
        if (sim == null || sim.isEmpty()) {
            Logger.w(TAG, "Invalid SIM value");
            return;
        }

        Logger.i(TAG, "Updating SIM to: " + sim);

        // Set SIM switch GPIO according to SIM type
        String value;
        if (SIM_TYPE_ESIM.equalsIgnoreCase(sim)) {
            value = "1";
            Logger.i(TAG, "Setting SIM switch to eSIM (1)");
        } else if (SIM_TYPE_USIM.equalsIgnoreCase(sim)) {
            value = "0";
            Logger.i(TAG, "Setting SIM switch to USIM (0)");
        } else {
            Logger.w(TAG, "Unknown SIM type: " + sim + ", ignoring");
            return;
        }
        // Write to GPIO file
        updateSIM(value);
    }

    private boolean updateSIM(String modeStr) {
        boolean result = false;
        int CARD_POWER_UP = 1;
        int CARD_POWER_DOWN = 0;
        Writer outputStream = null;
        try {
            File sim2SwitchFile = new File(SIM_SWITCH_PATH);
            if ((sim2SwitchFile != null) && !sim2SwitchFile.exists()) {
                Logger.i(TAG, "Switch sim2 is NOT supported.");
                return false;
            }
            Logger.d(TAG, "setSimPowerStateForSlot CARD_POWER_DOWN");
            mTelephonyManager.setSimPowerStateForSlot(1, CARD_POWER_DOWN);

            Thread.sleep(SIM_POWER_DOWN_DELAY_MS);
            outputStream = new BufferedWriter(new FileWriter(sim2SwitchFile));
            outputStream.write(modeStr);
            outputStream.flush();
            Thread.sleep(SIM_SWITCH_DELAY_MS);

            Logger.d(TAG, "setSimPowerStateForSlot CARD_POWER_UP");
            mTelephonyManager.setSimPowerStateForSlot(1, CARD_POWER_UP);
            result = true;
        } catch (Exception ex) {
            Logger.e(TAG, "SwitchSIM2 sysfs access error! Exception : " + ex);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException ioEx) {
                }
            }
        }
        return result;
    }

    /**
     * Write to file.
     */
    private void writeToFile(String path, String value) throws IOException {
        File file = new File(path);
        if (!file.exists() || !file.canWrite()) {
            throw new IOException("Cannot write to file: " + path);
        }

        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            fos.write(value.getBytes());
            fos.flush();
            Logger.i(TAG, "Successfully wrote '" + value + "' to " + path);
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException ioEx) {
                    Logger.e(TAG, "Error closing file: " + ioEx.getMessage(), ioEx);
                }
            }
        }
    }



    /**
     * Handle AGPS configuration.
     * Users can customize processing logic.
     */
    private void handleAgps(boolean agpsEnabled) {
        // Notify AGPS module
        Logger.i(TAG, "Updating AGPS to: " + agpsEnabled);
        Intent intent = new Intent(ACTION_CONFIG_UPDATED);
        intent.putExtra(EXTRA_CONFIG_KEY, ConfigKeys.AGPS_CONFIG);
        intent.putExtra(ConfigKeys.AGPS_CONFIG, agpsEnabled);
        sendBroadcast(intent);
    }

    /**
     * Handle Impact Detection configuration.
     * Set Settings.System collision_enabled according to impactDetection value.
     */
    private void handleImpactDetection(boolean impactDetection) {
        Logger.i(TAG, "Updating Impact Detection to: " + impactDetection);

        try {
            mIsUpdatingFromConfig = true;

            // 转换boolean到int (true=1, false=0)
            int collisionEnabled = impactDetection ? 1 : 0;

            // 写入Settings.System
            boolean success = Settings.System.putInt(getContentResolver(), KEY_COLLISION_ENABLED, collisionEnabled);

            if (success) {
                Logger.i(TAG, "Successfully updated Settings.System collision_enabled to: " + collisionEnabled);
            } else {
                Logger.e(TAG, "Failed to update Settings.System collision_enabled");
            }

        } catch (Exception es) {
            Logger.e(TAG, "Exception updating collision_enabled: " + es.getMessage(), es);
        } finally {
            mIsUpdatingFromConfig = false;
        }
    }

    /**
     * Generic configuration processing method.
     */
    private void handleGenericConfig(String key, Object value) {
        Logger.i(TAG, "Handling generic config: " + key + " = " + value);

        // Create generic broadcast
        Intent intent = new Intent(ACTION_CONFIG_UPDATED);
        intent.putExtra(EXTRA_CONFIG_KEY, key);

        // Add different Extras based on value type
        if (value instanceof String) {
            intent.putExtra(key, (String)value);
        } else if (value instanceof Integer) {
            intent.putExtra(key, (Integer)value);
        } else if (value instanceof Boolean) {
            intent.putExtra(key, (Boolean)value);
        } else if (value instanceof Float) {
            intent.putExtra(key, (Float)value);
        } else if (value instanceof JsonElement) {
            intent.putExtra(key, value.toString());
        }

        sendBroadcast(intent);
    }

    /**
     * Save impact detection value to ConfigTool.
     */
    private void saveImpactDetectionToConfig(boolean impactDetection) {
        if (mConfigHelper == null || !mConfigHelper.isServiceConnected()) {
            Logger.w(TAG, "ConfigTool service not connected, cannot save impactDetection");
            return;
        }

        try {
            // 构造JSON数据
            JsonObject configData = new JsonObject();
            configData.addProperty("impactDetection", impactDetection);

            // 调用ConfigTool的saveConfig方法
            boolean success = mConfigHelper.saveConfig(configData.toString());
            Logger.i(TAG, "Save impactDetection to config result: " + success);

        } catch (Exception es) {
            Logger.e(TAG, "Exception saving impactDetection: " + es.getMessage(), es);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.i(TAG, "SettingsManagerService onStartCommand");
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.i(TAG, "SettingsManagerService onDestroy");

        // Remove all callbacks
        mHandler.removeCallbacksAndMessages(null);

        // Unregister ContentObserver
        if (mCollisionEnabledObserver != null) {
            try {
                getContentResolver().unregisterContentObserver(mCollisionEnabledObserver);
                Logger.i(TAG, "ContentObserver unregistered");
            } catch (Exception es) {
                Logger.e(TAG, "Error unregistering ContentObserver: " + es.getMessage(), es);
            }
        }

        // Unbind ConfigTool service
        if (mConfigHelper != null) {
            // Remove all listeners
            mConfigHelper.removeListener(null);

            // Unbind service
            Context context = getApplicationContext();
            if (context != null) {
                mConfigHelper.unbindService(context);
            }
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // No binding needed
    }

    // Static getter methods

    /**
     * Get string configuration.
     */
    public static String getString(String key, String defaultValue) {
        Object value = sConfigMap.get(key);
        if (value instanceof String) {
            return (String)value;
        }
        return defaultValue;
    }

    /**
     * Get boolean configuration.
     */
    public static boolean getBoolean(String key, boolean defaultValue) {
        Object value = sConfigMap.get(key);
        if (value instanceof Boolean) {
            return (Boolean)value;
        }
        return defaultValue;
    }

    /**
     * Get integer configuration.
     */
    public static int getInt(String key, int defaultValue) {
        Object value = sConfigMap.get(key);
        if (value instanceof Integer) {
            return (Integer)value;
        }
        return defaultValue;
    }

    /**
     * Get float configuration.
     */
    public static float getFloat(String key, float defaultValue) {
        Object value = sConfigMap.get(key);
        if (value instanceof Float) {
            return (Float)value;
        }
        return defaultValue;
    }

    /**
     * Get JSON configuration.
     */
    public static JsonElement getJson(String key) {
        Object value = sConfigMap.get(key);
        if (value instanceof JsonElement) {
            return (JsonElement)value;
        }
        return null;
    }

    /**
     * Get SIM card type.
     */
    public static String getSimConfig() {
        return getString(ConfigKeys.SIM_CONFIG, "");
    }

    /**
     * Get AGPS status.
     */
    public static boolean isAgpsEnabled() {
        return getBoolean(ConfigKeys.AGPS_CONFIG, false);
    }

    /**
     * Get Impact Detection status.
     */
    public static boolean isImpactDetectionEnabled() {
        return getBoolean(ConfigKeys.IMPACT_DETECTION, false);
    }





    /**
     * Handle AppInfo configuration.
     */
    private void handleAppInfo(JsonElement appInfoElement) {
        if (appInfoElement == null || !appInfoElement.isJsonArray()) {
            Logger.w(TAG, "Invalid appInfo configuration");
            return;
        }

        try {
            Logger.i(TAG, "Processing appInfo configuration");

            // Parse AppInfo list
            List<AppInfo> appInfoList = parseAppInfoList(appInfoElement);
            Logger.i(TAG, "Parsed " + appInfoList.size() + " app configurations");

            // Process each application configuration
            for (AppInfo appInfo : appInfoList) {
                processAppInfo(appInfo);
            }

            // Update static configuration storage
            updateConfig(ConfigKeys.APP_INFO, appInfoList);

            Logger.i(TAG, "AppInfo configuration processed and cached successfully");

        } catch (Exception ex) {
            Logger.e(TAG, "Error processing appInfo configuration: " + ex.getMessage(), ex);
        }
    }

    /**
     * Parse AppInfo list.
     */
    private List<AppInfo> parseAppInfoList(JsonElement appInfoElement) {
        List<AppInfo> appInfoList = new ArrayList<>();

        try {
            JsonArray jsonArray = appInfoElement.getAsJsonArray();
            Type listType = new TypeToken<List<AppInfo>>(){}.getType();
            appInfoList = mGson.fromJson(jsonArray, listType);

            // Validate parsing result
            if (appInfoList != null) {
                for (AppInfo appInfo : appInfoList) {
                    if (appInfo.getPackageName() == null || appInfo.getPackageName().trim().isEmpty()) {
                        Logger.w(TAG, "Invalid appInfo: missing packageName");
                        continue;
                    }
                    Logger.d(TAG, "Parsed appInfo: " + appInfo.toString());
                }
            } else {
                appInfoList = new ArrayList<>();
            }

        } catch (Exception ex) {
            Logger.e(TAG, "Error parsing appInfo list: " + ex.getMessage(), ex);
            appInfoList = new ArrayList<>();
        }

        return appInfoList;
    }

    /**
     * Process individual application configuration.
     */
    private void processAppInfo(AppInfo appInfo) {
        String packageName = appInfo.getPackageName();
        Logger.i(TAG, "Processing app: " + packageName);

        try {
            // 1. Handle permission granting
            handleAppPermissions(packageName, appInfo.getPermissionList());

            // 2. Handle Doze whitelist
            handleAppDozeWhitelist(packageName);

            // 3. Handle application startup
            if (appInfo.isAutoBoot()) {
                handleAppAutoStart(packageName);
            }

        } catch (Exception ex) {
            Logger.e(TAG, "Error processing app " + packageName + ": " + ex.getMessage(), ex);
        }
    }

    /**
     * Handle application permissions.
     */
    private void handleAppPermissions(String packageName, List<String> permissions) {
        if (permissions.isEmpty()) {
            Logger.d(TAG, "No permissions to grant for " + packageName);
            return;
        }

        Logger.i(TAG, "Processing " + permissions.size() + " permissions for " + packageName);

        if (isAppInstalled(packageName)) {
            // Application is installed, grant permissions immediately
            grantPermissionsToApp(packageName, permissions);
        } else {
            // Application not installed, store preinstall permissions
            storePreinstallPermissions(packageName, permissions);
        }
    }

    /**
     * Grant application permissions.
     */
    private void grantPermissionsToApp(String packageName, List<String> permissions) {
        PackageManager pm = getPackageManager();
        int successCount = 0;
        int appOpsCount = 0;

        for (String permission : permissions) {
            try {
                // Check if it's a special permission that needs AppOps handling
                if (APPOPS_PERMISSIONS.containsKey(permission)) {
                    if (grantAppOpsPermission(packageName, permission)) {
                        appOpsCount++;
                        successCount++;
                        Logger.i(TAG, "Granted AppOps permission " + permission + " to " + packageName);
                    } else {
                        Logger.w(TAG, "Failed to grant AppOps permission " + permission + " to " + packageName);
                    }
                } else {
                    // Regular runtime permission
                    pm.grantRuntimePermission(packageName, permission, android.os.Process.myUserHandle());
                    successCount++;
                    Logger.i(TAG, "Granted permission " + permission + " to " + packageName);
                }
            } catch (Exception ex) {
                Logger.w(TAG, "Failed to grant permission " + permission + " to " + packageName + ": " + ex.getMessage());
            }
        }

        Logger.i(TAG, "Successfully granted " + successCount + "/" + permissions.size()
                + " permissions to " + packageName + " (Runtime: " + (successCount - appOpsCount)
                + ", AppOps: " + appOpsCount + ")");
    }

    /**
     * Grant special permissions through AppOpsManager.
     */
    private boolean grantAppOpsPermission(String packageName, String permission) {
        try {
            // Get AppOpsManager
            AppOpsManager appOps = (AppOpsManager) getSystemService(Context.APP_OPS_SERVICE);
            if (appOps == null) {
                Logger.e(TAG, "AppOpsManager not available");
                return false;
            }

            // Get application UID
            PackageManager pm = getPackageManager();
            ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
            int uid = appInfo.uid;

            // Get corresponding AppOps operation string
            String appOpsStr = APPOPS_PERMISSIONS.get(permission);
            if (appOpsStr == null) {
                Logger.e(TAG, "No AppOps mapping found for permission: " + permission);
                return false;
            }

            // Set AppOps permission to allow
            appOps.setMode(appOpsStr, uid, packageName, AppOpsManager.MODE_ALLOWED);

            Logger.d(TAG, "Successfully set AppOps " + appOpsStr + " to MODE_ALLOWED for "
                    + packageName + " (uid: " + uid + ")");
            return true;

        } catch (PackageManager.NameNotFoundException nameEx) {
            Logger.e(TAG, "Package not found: " + packageName, nameEx);
            return false;
        } catch (Exception ex) {
            Logger.e(TAG, "Error granting AppOps permission " + permission + " to " + packageName
                    + ": " + ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * Verify if AppOps permission has been granted.
     */
    private boolean verifyAppOpsPermission(String packageName, String permission) {
        try {
            AppOpsManager appOps = (AppOpsManager) getSystemService(Context.APP_OPS_SERVICE);
            if (appOps == null) {
                return false;
            }

            PackageManager pm = getPackageManager();
            ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
            int uid = appInfo.uid;

            String appOpsStr = APPOPS_PERMISSIONS.get(permission);
            if (appOpsStr == null) {
                return false;
            }

            // Check AppOps permission status
            int mode = appOps.checkOpNoThrow(appOpsStr, uid, packageName);
            boolean isAllowed = (mode == AppOpsManager.MODE_ALLOWED);

            Logger.d(TAG, "AppOps " + appOpsStr + " for " + packageName + ": " +
                    (isAllowed ? "ALLOWED" : "DENIED") + " (mode: " + mode + ")");

            return isAllowed;

        } catch (Exception ex) {
            Logger.e(TAG, "Error verifying AppOps permission " + permission + " for " + packageName
                    + ": " + ex.getMessage());
            return false;
        }
    }

    /**
     * Store preinstall permissions.
     */
    private void storePreinstallPermissions(String packageName, List<String> permissions) {
        try {
            String permissionString = String.join(",", permissions);

            getSharedPreferences("preinstall_permissions", Context.MODE_PRIVATE)
                .edit()
                .putString(packageName, permissionString)
                .apply();

            Logger.i(TAG, "Stored preinstall permissions for " + packageName + ": " + permissions);
        } catch (Exception ex) {
            Logger.e(TAG, "Failed to store preinstall permissions for " + packageName, ex);
        }
    }

    /**
     * Handle Doze whitelist.
     */
    private void handleAppDozeWhitelist(String packageName) {
        try {
            // Directly call DeviceIdleController service API
            IBinder deviceIdleService = ServiceManager.getService(Context.DEVICE_IDLE_CONTROLLER);
            if (deviceIdleService != null) {
                IDeviceIdleController deviceIdleController = IDeviceIdleController.Stub.asInterface(deviceIdleService);
                deviceIdleController.addPowerSaveWhitelistApp(packageName);
                Logger.i(TAG, "Added " + packageName + " to Doze whitelist");
            } else {
                Logger.w(TAG, "DeviceIdleController service not available");
            }
        } catch (Exception ex) {
            Logger.w(TAG, "Failed to add " + packageName + " to Doze whitelist: " + ex.getMessage());
        }
    }



    /**
     * Handle application auto-start.
     */
    private void handleAppAutoStart(String packageName) {
        try {
            if (isAppInstalled(packageName)) {
                Intent launchIntent = getPackageManager().getLaunchIntentForPackage(packageName);
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(launchIntent);
                    Logger.i(TAG, "Auto-started app: " + packageName);
                } else {
                    Logger.w(TAG, "No launch intent found for " + packageName);
                }
            } else {
                Logger.w(TAG, "Cannot auto-start " + packageName + ": app not installed");
            }
        } catch (Exception ex) {
            Logger.w(TAG, "Failed to auto-start " + packageName + ": " + ex.getMessage());
        }
    }

    /**
     * Check if application is installed.
     */
    private boolean isAppInstalled(String packageName) {
        try {
            getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    /**
     * Register application installation listener.
     */
    private void registerPackageInstallReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_PACKAGE_ADDED);
        filter.addAction(Intent.ACTION_PACKAGE_REPLACED);
        filter.addDataScheme("package");

        registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                String packageName = intent.getData().getSchemeSpecificPart();

                Logger.i(TAG, "Package event: " + action + " for " + packageName);

                if (Intent.ACTION_PACKAGE_ADDED.equals(action) || Intent.ACTION_PACKAGE_REPLACED.equals(action)) {
                    handlePackageInstalled(packageName);
                }
            }
        }, filter);

        Logger.i(TAG, "Package install receiver registered");
    }

    /**
     * Handle application installation event.
     */
    private void handlePackageInstalled(String packageName) {
        try {
            // Check if there are preinstall permissions to grant
            SharedPreferences prefs = getSharedPreferences("preinstall_permissions", Context.MODE_PRIVATE);
            String preinstallPermissions = prefs.getString(packageName, null);

            if (preinstallPermissions != null) {
                List<String> permissions = Arrays.asList(preinstallPermissions.split(","));
                Logger.i(TAG, "Found preinstall permissions for " + packageName + ": " + permissions);

                // Grant preinstall permissions
                grantPermissionsToApp(packageName, permissions);

                // Clear preinstall permission records
                prefs.edit().remove(packageName).apply();
                Logger.i(TAG, "Cleared preinstall permissions for " + packageName);
            }

            // Check if need to add to Doze whitelist and auto-start
            checkAppInfoForInstalledApp(packageName);

        } catch (Exception ex) {
            Logger.e(TAG, "Error handling package install for " + packageName, ex);
        }
    }

    /**
     * Check AppInfo configuration for newly installed applications.
     */
    private void checkAppInfoForInstalledApp(String packageName) {
        try {
            @SuppressWarnings("unchecked")
            List<AppInfo> appInfoList = (List<AppInfo>) sConfigMap.get(ConfigKeys.APP_INFO);

            if (appInfoList != null) {
                for (AppInfo appInfo : appInfoList) {
                    if (packageName.equals(appInfo.getPackageName())) {
                        Logger.i(TAG, "Found AppInfo config for newly installed app: " + packageName);

                        // Handle Doze whitelist
                        handleAppDozeWhitelist(packageName);

                        // Handle auto-start
                        if (appInfo.isAutoBoot()) {
                            handleAppAutoStart(packageName);
                        }
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            Logger.e(TAG, "Error checking AppInfo for installed app " + packageName, ex);
        }
    }

    /**
     * Get AppInfo configuration list.
     */
    public static List<AppInfo> getAppInfoList() {
        @SuppressWarnings("unchecked")
        List<AppInfo> appInfoList = (List<AppInfo>) sConfigMap.get(ConfigKeys.APP_INFO);
        return appInfoList != null ? new ArrayList<>(appInfoList) : new ArrayList<>();
    }

    /**
     * Get AppInfo configuration for specified application.
     */
    public static AppInfo getAppInfo(String packageName) {
        List<AppInfo> appInfoList = getAppInfoList();
        for (AppInfo appInfo : appInfoList) {
            if (packageName.equals(appInfo.getPackageName())) {
                return appInfo;
            }
        }
        return null;
    }

    /**
     * Check if application is configured for auto-start.
     */
    public static boolean isAppAutoBootEnabled(String packageName) {
        AppInfo appInfo = getAppInfo(packageName);
        return appInfo != null && appInfo.isAutoBoot();
    }

    /**
     * Get permission list configured for application.
     */
    public static List<String> getAppConfiguredPermissions(String packageName) {
        AppInfo appInfo = getAppInfo(packageName);
        return appInfo != null ? appInfo.getPermissionList() : new ArrayList<>();
    }
}