/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.MonitorTool;

import android.Manifest;
import android.app.AppOpsManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.PowerManager;
import android.os.RemoteException;
import android.os.StatFs;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.view.View;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.thundercomm.MonitorTool.utils.ConfigKeys;
import com.thundercomm.MonitorTool.utils.Logger;
import com.thundercomm.configtool.api.ConfigToolServiceManager;
import com.thundercomm.configtool.api.IConfigToolServiceConnectCallback;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Random;
import java.util.ArrayList;
import java.util.Map;

/**
 * Main interface
 * Display storage status, provide test functions
 */
public class MainActivity extends AppCompatActivity implements Logger.LogCallback {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_STORAGE_PERMISSION = 100;
    private static final int REQUEST_MANAGE_STORAGE = 101;

    // Constants
    private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String STORAGE_STATUS_FORMAT =
            "Storage Status:\nTotal Space: %d MB\nAvailable Space: %d MB\nUsed: %d%%";
    private static final String STORAGE_WARNING_MESSAGE = "\n\nWarning: Insufficient storage space!";
    private static final String STORAGE_ERROR_MESSAGE = "Cannot get storage status";
    private static final String TEST_FILE_FOLDER = "MonitorTool/test_files";
    private static final int MAX_LOG_LENGTH = 10000;
    private static final int BYTES_PER_MB = 1024 * 1024;
    private static final long DEFAULT_TEST_FILE_SIZE_BYTES = 10 * 1024 * 1024; // 10MB

    // Timing constants
    private static final long SESSION_DURATION_DIVISOR = 1000; // Convert milliseconds to seconds
    private static final int PERCENTAGE_MULTIPLIER = 100;
    private static final int BUFFER_SIZE_BYTES = 1024;
    private static final long BYTES_TO_KB_DIVISOR = 1024;
    private static final long SIM_POWER_DOWN_DELAY_MS = 1000; // 1 second
    private static final long SIM_SWITCH_DELAY_MS = 500; // 0.5 seconds
    private static final long THREAD_DELAY_MS = 1000; // 1 second

    // Android version constants
    private static final int ANDROID_R_API_LEVEL = 30; // Android 11

    // SIM card related constants
    private static final String SIM_SWITCH_PATH = "/sys/devices/platform/soc/soc:gpio_info/sim_switch_gpio122";
    private static final String SIM_TYPE_ESIM = "esim";
    private static final String SIM_TYPE_USIM = "usim";

    private TextView mStatusText;
    private TextView mLogText;
    private ScrollView mLogScrollView;
    private Button mCheckButton;
    private Button mCreateFileButton;
    private Button mClearLogButton;
    private Button mTestConfigButton;
    private Button mTestAppMonitorButton;
    private Button mSwitchToEsimButton;
    private Button mSwitchToUsimButton;
    private Button mCheckDozeWhitelistButton;
    private Button mCheckAppInfoStatusButton;

    private MemoryControlService mService;
    private MonitorAppService mAppMonitorService;
    private boolean mBound = false;
    private boolean mAppMonitorBound = false;
    private Handler mHandler;
    private StringBuilder mLogBuilder = new StringBuilder();
    private int mTestFileCount = 0;
    private long mSessionStartTime;

    // ConfigTool service
    private ConfigToolServiceManager mConfigManager;
    private TelephonyManager mTelephonyManager;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.i(TAG, "onCreate - Application launched");
        mSessionStartTime = System.currentTimeMillis();

        setContentView(R.layout.activity_main);

        Logger.d(TAG, "Initializing UI components");
        initViews();

        mHandler = new Handler(Looper.getMainLooper());
        Logger.setLogCallback(this);

        // Start service
        Logger.i(TAG, "Starting MemoryControlService service");
        startService(new Intent(this, MemoryControlService.class));

        // Start app monitoring service
        Logger.i(TAG, "Starting MonitorAppService service");
        startService(new Intent(this, MonitorAppService.class));

        // Start settings manager service
        Logger.i(TAG, "Starting SettingsManagerService service");
        startService(new Intent(this, SettingsManagerService.class));

        // Log system information
        Logger.logSystemInfo(TAG);

        // Check permissions
        checkStoragePermissions();

        // Update storage status
        updateStorageStatus();
        mTelephonyManager = this.getSystemService(TelephonyManager.class);
        Logger.i(TAG, "MainActivity initialization completed");
    }

    @Override
    protected void onStart() {
        super.onStart();
        Logger.d(TAG, "onStart - Binding services");

        // Bind MemoryControl service
        Intent intent = new Intent(this, MemoryControlService.class);
        bindService(intent, mConnection, BIND_AUTO_CREATE);

        // Bind MonitorApp service
        Intent appMonitorIntent = new Intent(this, MonitorAppService.class);
        bindService(appMonitorIntent, mAppMonitorConnection, BIND_AUTO_CREATE);

        // Bind ConfigTool service
        bindConfigToolService();
    }

    @Override
    protected void onStop() {
        super.onStop();
        Logger.d(TAG, "onStop - Unbinding services");

//        // Unbind ConfigTool service
//        unbindConfigToolService();
//
//        // Unbind MonitorApp service
//        if (mAppMonitorBound) {
//            unbindService(mAppMonitorConnection);
//            mAppMonitorBound = false;
//            mAppMonitorService = null;
//        }
//
//        // Unbind MemoryControl service
//        if (mBound) {
//            unbindService(mConnection);
//            mBound = false;
//            mService = null;
//        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        long sessionDuration = System.currentTimeMillis() - mSessionStartTime;
        Logger.i(TAG, "onDestroy - Application exit, session duration: "
                + (sessionDuration / SESSION_DURATION_DIVISOR) + " seconds");
        Logger.setLogCallback(null);

        // Unbind ConfigTool service
        if (mConfigManager != null) {
            mConfigManager.unbindService(this);
        }
    }

    /**
     * Initialize views
     */
    private void initViews() {
        Logger.d(TAG, "Initializing view components");

        mStatusText = findViewById(R.id.statusText);
        mLogText = findViewById(R.id.logText);
        mLogScrollView = findViewById(R.id.logScrollView);
        mCheckButton = findViewById(R.id.checkButton);
        mCreateFileButton = findViewById(R.id.createFileButton);
        mClearLogButton = findViewById(R.id.clearLogButton);
        mTestConfigButton = findViewById(R.id.testConfigButton);
        mTestAppMonitorButton = findViewById(R.id.testAppMonitorButton);
        mSwitchToEsimButton = findViewById(R.id.switchToEsimButton);
        mSwitchToUsimButton = findViewById(R.id.switchToUsimButton);
        mCheckDozeWhitelistButton = findViewById(R.id.checkDozeWhitelistButton);
        mCheckAppInfoStatusButton = findViewById(R.id.checkAppInfoStatusButton);

        Logger.d(TAG, "Setting button click listeners");

        // Check storage button
        mCheckButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Logger.i(TAG, "User clicked 'Check Storage' button");
                checkStorage();
            }
        });

        // Create test file button
        mCreateFileButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Logger.i(TAG, "User clicked 'Create Test File' button");
                createTestFile();
            }
        });

        // Clear log button
        mClearLogButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Logger.d(TAG, "User clicked 'Clear Log' button");
                clearLog();
            }
        });

        // Test configuration button
        if (mTestConfigButton != null) {
            mTestConfigButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Logger.i(TAG, "User clicked 'Test Config' button");
                    testConfigTool();
                }
            });
        }

        // Test app monitoring button
        if (mTestAppMonitorButton != null) {
            mTestAppMonitorButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Logger.i(TAG, "User clicked 'Test App Monitoring' button");
                    testAppMonitor();
                }
            });
        }

        // SIM switch buttons
        if (mSwitchToEsimButton != null) {
            mSwitchToEsimButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Logger.i(TAG, "User clicked 'Switch to eSIM' button");
                    handleSim(SIM_TYPE_ESIM);
                }
            });
        }

        if (mSwitchToUsimButton != null) {
            mSwitchToUsimButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Logger.i(TAG, "User clicked 'Switch to USIM' button");
                    handleSim(SIM_TYPE_USIM);
                }
            });
        }

        // Check AppInfo Status button
        if (mCheckAppInfoStatusButton != null) {
            mCheckAppInfoStatusButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Logger.i(TAG, "User clicked 'Check AppInfo Status' button");
                    checkAppInfoStatus();
                }
            });
        }

        Logger.d(TAG, "View components initialization complete");
    }

    /**
     * Update storage status
     */
    private void updateStorageStatus() {
        Logger.v(TAG, "Updating storage status");

        try {
            StatFs stat = new StatFs(Environment.getExternalStorageDirectory().getPath());
            long blockSize = stat.getBlockSizeLong();
            long totalBlocks = stat.getBlockCountLong();
            long availableBlocks = stat.getAvailableBlocksLong();

            long totalSize = totalBlocks * blockSize;
            long availableSize = availableBlocks * blockSize;

            long totalSizeMB = totalSize / BYTES_PER_MB;
            long availableSizeMB = availableSize / BYTES_PER_MB;
            int usedPercent = (int) (PERCENTAGE_MULTIPLIER - (availableBlocks * PERCENTAGE_MULTIPLIER / totalBlocks));

            Logger.d(TAG, "Storage status: Total space=" + totalSizeMB + "MB, Available space=" + availableSizeMB +
                    "MB, Used percentage=" + usedPercent + "%");

            String statusText = String.format(Locale.getDefault(),
                    STORAGE_STATUS_FORMAT,
                    totalSizeMB, availableSizeMB, usedPercent);

            mStatusText.setText(statusText);

            // If available space is below threshold, show warning
            if (mBound && mService != null) {
                long minStorageMB = mService.getMinStorageMB();
                if (availableSizeMB < minStorageMB) {
                    Logger.w(TAG, "Storage space warning: Available space(" + availableSizeMB + 
                            "MB) below threshold(" + minStorageMB + "MB)");
                    mStatusText.append(STORAGE_WARNING_MESSAGE);
                }
            }
        } catch (Exception ex) {
            Logger.e(TAG, "Failed to update storage status: " + ex.getMessage(), ex);
            mStatusText.setText(STORAGE_ERROR_MESSAGE);
        }
    }

    /**
     * Check storage space
     */
    private void checkStorage() {
        Logger.i(TAG, "Manually triggering storage space check");
        addLog("Checking storage space...");

        // Update storage status
        updateStorageStatus();

        // If service is bound, call service method directly
        if (mBound && mService != null) {
            // Service will execute check logic internally
            Logger.d(TAG, "Service bound, check executed via service");
        } else {
            Logger.w(TAG, "Service not bound, check cannot be executed");
            addLog("Service not bound, check cannot be executed");
        }
    }

    /**
     * Check storage permissions
     */
    private void checkStoragePermissions() {
        Logger.d(TAG, "Checking storage permissions");

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ uses MANAGE_EXTERNAL_STORAGE permission
            if (!Environment.isExternalStorageManager()) {
                Logger.i(TAG, "Requesting MANAGE_EXTERNAL_STORAGE permission");
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                Uri uri = Uri.fromParts("package", getPackageName(), null);
                intent.setData(uri);
                try {
                    startActivityForResult(intent, REQUEST_MANAGE_STORAGE);
                } catch (Exception ex) {
                    Logger.e(TAG, "Cannot open permission settings page: " + ex.getMessage(), ex);
                    Toast.makeText(this, "Please manually grant storage permissions", Toast.LENGTH_LONG).show();
                }
            } else {
                Logger.d(TAG, "MANAGE_EXTERNAL_STORAGE permission already granted");
            }
        } else {
            // Android 10 and below use READ_EXTERNAL_STORAGE and WRITE_EXTERNAL_STORAGE permissions
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED) {
                Logger.i(TAG, "Requesting READ_EXTERNAL_STORAGE and WRITE_EXTERNAL_STORAGE permissions");
                ActivityCompat.requestPermissions(this,
                        new String[]{
                                Manifest.permission.READ_EXTERNAL_STORAGE,
                                Manifest.permission.WRITE_EXTERNAL_STORAGE
                        },
                        REQUEST_STORAGE_PERMISSION);
            } else {
                Logger.d(TAG, "READ_EXTERNAL_STORAGE and WRITE_EXTERNAL_STORAGE permissions already granted");
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
            @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_STORAGE_PERMISSION) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                Logger.i(TAG, "Storage permissions granted");
                Toast.makeText(this, "Storage permissions granted", Toast.LENGTH_SHORT).show();
            } else {
                Logger.w(TAG, "Storage permissions denied");
                Toast.makeText(this, "Storage permissions are required to work properly", Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_MANAGE_STORAGE) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                if (Environment.isExternalStorageManager()) {
                    Logger.i(TAG, "MANAGE_EXTERNAL_STORAGE permission granted");
                    Toast.makeText(this, "Storage permissions granted", Toast.LENGTH_SHORT).show();
                } else {
                    Logger.w(TAG, "MANAGE_EXTERNAL_STORAGE permission denied");
                    Toast.makeText(this, "Storage permissions are required to work properly", Toast.LENGTH_LONG).show();
                }
            }
        }
    }

    /**
     * Create test file
     */
    private void createTestFile() {
        Logger.i(TAG, "Starting to create test file");
        addLog("Creating test file...");

        // Check permissions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                Logger.w(TAG, "No MANAGE_EXTERNAL_STORAGE permission, cannot create test file");
                addLog("No storage permissions, cannot create test file");
                checkStoragePermissions();
                return;
            }
        } else if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            Logger.w(TAG, "No WRITE_EXTERNAL_STORAGE permission, cannot create test file");
            addLog("No storage permissions, cannot create test file");
            checkStoragePermissions();
            return;
        }

        // Create a test file with random data
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // Create test directory
                    File storageDir = new File(Environment.getExternalStorageDirectory(), getTestFileFolder());
                    if (!storageDir.exists()) {
                        boolean created = storageDir.mkdirs();
                        if (!created) {
                            Logger.e(TAG, "Failed to create test directory: " + storageDir.getAbsolutePath());
                            addLog("Failed to create test directory");
                            return;
                        }
                    }

                    // Generate test file name with timestamp
                    SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.getDefault());
                    String timestamp = sdf.format(new Date()).replace(" ", "_").replace(":", "-");

                    mTestFileCount++;
                    File testFile = new File(storageDir, "test_file_" + mTestFileCount + "_" + timestamp + ".dat");

                    // Create file with random data
                    FileOutputStream fos = new FileOutputStream(testFile);

                    // Generate 10MB of random data (configurable)
                    long fileSize = getTestFileSizeBytes();
                    byte[] buffer = new byte[BUFFER_SIZE_BYTES];
                    Random random = new Random();

                    long bytesWritten = 0;
                    while (bytesWritten < fileSize) {
                        random.nextBytes(buffer);
                        int toWrite = (int) Math.min(buffer.length, fileSize - bytesWritten);
                        fos.write(buffer, 0, toWrite);
                        bytesWritten += toWrite;
                    }

                    fos.close();

                    float fileSizeMB = (float) fileSize / BYTES_PER_MB;
                    Logger.i(TAG, "Test file created: " + testFile.getAbsolutePath() + " (" + fileSizeMB + " MB)");

                    final String successMessage = "Test file created:\n" + testFile.getAbsolutePath()
                            + "\nSize: " + String.format(Locale.getDefault(), "%.2f", fileSizeMB) + " MB";
                    addLog(successMessage);

                    // Update storage status
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            updateStorageStatus();
                        }
                    });

                } catch (IOException ioEx) {
                    Logger.e(TAG, "Failed to create test file: " + ioEx.getMessage(), ioEx);
                    addLog("Failed to create test file: " + ioEx.getMessage());
                }
            }
        }).start();
    }

    /**
     * Get test file folder name
     * @return Test file folder name
     */
    private static String getTestFileFolder() {
        return TEST_FILE_FOLDER;
    }

    /**
     * Get test file size in bytes
     * @return Test file size in bytes
     */
    private static long getTestFileSizeBytes() {
        return DEFAULT_TEST_FILE_SIZE_BYTES;
    }

    /**
     * Add log
     * @param log Log message
     */
    private void addLog(final String log) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                // Add timestamp
                SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.getDefault());
                String timestamp = sdf.format(new Date());

                String formattedLog = "[" + timestamp + "] " + log;
                mLogBuilder.append(formattedLog).append("\n");

                // Trim log if too long
                if (mLogBuilder.length() > getMaxLogLength()) {
                    mLogBuilder.delete(0, mLogBuilder.length() - getMaxLogLength());
                    // Find first newline to start from a clean line
                    int firstNewline = mLogBuilder.indexOf("\n");
                    if (firstNewline > 0) {
                        mLogBuilder.delete(0, firstNewline + 1);
                    }
                }

                mLogText.setText(mLogBuilder.toString());

                // Scroll to bottom
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        mLogScrollView.fullScroll(View.FOCUS_DOWN);
                    }
                });
            }
        });
    }

    /**
     * Clear log
     */
    private void clearLog() {
        Logger.d(TAG, "Clearing UI log");

        mHandler.post(new Runnable() {
            @Override
            public void run() {
                mLogBuilder = new StringBuilder();
                mLogText.setText("");
            }
        });
    }

    /**
     * Service connection callback
     */
    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Logger.i(TAG, "Service connected: " + name.getClassName());

            // Save service reference
            MemoryControlService.LocalBinder binder = (MemoryControlService.LocalBinder) service;
            mService = binder.getService();
            mBound = true;

            // Set listener
            mService.setListener(new MemoryControlService.StorageMonitorListener() {
                @Override
                public void onStorageStatusUpdated(long availableSpaceMB, long minStorageMB) {
                    Logger.v(TAG, "Received storage status update: Available=" + availableSpaceMB
                            + "MB, Min threshold=" + minStorageMB + "MB");
                    updateStorageStatus();
                }

                @Override
                public void onFileDeleted(String filePath, long fileSize) {
                    Logger.d(TAG, "Received file deletion notification: " + filePath
                            + ", Size=" + (fileSize / BYTES_TO_KB_DIVISOR) + "KB");
                }
            });

            // Get service configuration
            long minStorageMB = mService.getMinStorageMB();
            long checkIntervalSeconds = mService.getCheckIntervalSeconds();
            Logger.i(TAG, "Service configuration: Min storage space=" + minStorageMB + "MB, Check interval=" + checkIntervalSeconds + "seconds");

            // Update storage status
            updateStorageStatus();

            addLog("Service connected");
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Logger.w(TAG, "Service disconnected: " + name.getClassName());
            mBound = false;
            mService = null;
            addLog("Service disconnected");
        }
    };

    /**
     * Log callback implementation
     */
    @Override
    public void onLog(final String message) {
        // Add directly to UI, do not call Logger again, otherwise it will cause a loop
        addLog(message);
    }

    /**
     * Bind ConfigTool service
     */
    private void bindConfigToolService() {
        try {
            // Check if ConfigTool app is installed
            try {
                PackageManager pm = getPackageManager();
                pm.getPackageInfo("com.thundercomm.configtool", 0);
                Logger.i(TAG, "ConfigTool app installed");
                addLog("ConfigTool app installed");
            } catch (PackageManager.NameNotFoundException e) {
                Logger.e(TAG, "ConfigTool app not installed, cannot bind service", e);
                addLog("Error: ConfigTool app not installed, cannot bind service");
                return;
            }

            // Get ConfigToolServiceManager instance
            mConfigManager = ConfigToolServiceManager.getInstance();
            Logger.d(TAG, "ConfigToolServiceManager instance obtained: " + (mConfigManager != null ? "Success" : "Failed"));

            // Set service connection callback
            mConfigManager.setServiceConnectCallback(new IConfigToolServiceConnectCallback() {
                @Override
                public void onServiceConnected() {
                    Logger.i(TAG, "ConfigTool service connected");
                    addLog("ConfigTool service connected");
                }

                @Override
                public void onServiceDisconnected() {
                    Logger.w(TAG, "ConfigTool service disconnected");
                    addLog("ConfigTool service disconnected");
                }

                @Override
                public void onConfigChanged(String modifiedFields, String addedFields, long timestamp) {
                    Logger.i(TAG, "Configuration updated: Modified fields=" + modifiedFields + ", Added fields=" + addedFields);
                    addLog("Configuration updated: Timestamp=" + timestamp);
                }
            });

            // Bind service
            mConfigManager.bindService(this);
            Logger.i(TAG, "Starting to bind ConfigTool service");
            addLog("Starting to bind ConfigTool service");
        } catch (Exception ex) {
            Logger.e(TAG, "Failed to bind ConfigTool service: " + ex.getMessage(), ex);
            addLog("Failed to bind ConfigTool service: " + ex.getMessage());
        }
    }

    /**
     * Unbind ConfigTool service
     */
    private void unbindConfigToolService() {
        if (mConfigManager != null) {
            try {
                mConfigManager.unbindService(this);
                Logger.i(TAG, "ConfigTool service unbound");
            } catch (Exception ex) {
                Logger.e(TAG, "Failed to unbind ConfigTool service: " + ex.getMessage(), ex);
            }
        }
    }



    /**
     * Test ConfigTool configuration
     */
    private void testConfigTool() {
        Logger.i(TAG, "Starting ConfigTool configuration test");
        addLog("===== Testing ConfigTool Configuration =====");

        if (mConfigManager == null) {
            Logger.e(TAG, "ConfigTool service not connected, please check service binding status!");
            addLog("ConfigTool service not connected, cannot test");
            addLog("Please confirm that ConfigTool service is started and bound correctly");
            return;
        }

        try {
            Logger.i(TAG, "Starting to get ConfigTool configuration parameters");
            addLog("Starting to get configuration...");

            // Test other configurations (not storageManagement since it's now handled locally)
            // Test SIM configuration
            String simConfig = mConfigManager.getConfigValue("sim");
            Logger.i(TAG, "SIM configuration: " + simConfig);
            addLog("SIM configuration: " + (simConfig != null ? "Obtained successfully" : "Failed to obtain"));

            // Test AGPS configuration
            String agpsConfig = mConfigManager.getConfigValue("aGps");
            Logger.i(TAG, "AGPS configuration: " + agpsConfig);
            addLog("AGPS configuration: " + (agpsConfig != null ? "Obtained successfully" : "Failed to obtain"));

            // Test App Info configuration
            String appInfoConfig = mConfigManager.getConfigValue("appInfo");
            Logger.i(TAG, "App Info configuration: " + appInfoConfig);
            addLog("App Info configuration: " + (appInfoConfig != null ? "Obtained successfully" : "Failed to obtain"));

            // Test rebootTarget configuration
            String rebootTargetConfig = mConfigManager.getConfigValue("rebootTarget");
            Logger.i(TAG, "Reboot Target configuration: " + rebootTargetConfig);

            addLog("Note: Storage management configuration is now handled locally from config.xml");

            Logger.i(TAG, "ConfigTool configuration test completed");
            addLog("===== Configuration test completed =====");
        } catch (Exception ex) {
            Logger.e(TAG, "Failed to test ConfigTool configuration: " + ex.getMessage(), ex);
            addLog("Test failed: " + ex.getMessage());
        }
    }

    /**
     * Format configuration value
     */
    private String formatValue(String value) {
        if (value == null) {
            return "null";
        } else if (value.isEmpty()) {
            return "Empty string";
        } else {
            return value;
        }
    }



    /**
     * MonitorApp service connection
     */
    private ServiceConnection mAppMonitorConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Logger.d(TAG, "MonitorApp service connected");
            MonitorAppService.LocalBinder binder = (MonitorAppService.LocalBinder) service;
            mAppMonitorService = binder.getService();
            mAppMonitorBound = true;
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Logger.d(TAG, "MonitorApp service disconnected");
            mAppMonitorService = null;
            mAppMonitorBound = false;
        }
    };

    /**
     * Handle SIM configuration.
     * Set SIM switch GPIO according to SIM type.
     */
    private void handleSim(String sim) {
        if (sim == null || sim.isEmpty()) {
            Logger.w(TAG, "Invalid SIM value");
            addLog("Invalid SIM value");
            return;
        }

        Logger.i(TAG, "Updating SIM to: " + sim);
        addLog("Updating SIM to: " + sim);

        // Set SIM switch GPIO according to SIM type
        String value;
        if (SIM_TYPE_ESIM.equalsIgnoreCase(sim)) {
            value = "1";
            Logger.i(TAG, "Setting SIM switch to eSIM (1)");
            addLog("Setting SIM switch to eSIM (1)");
        } else if (SIM_TYPE_USIM.equalsIgnoreCase(sim)) {
            value = "0";
            Logger.i(TAG, "Setting SIM switch to USIM (0)");
            addLog("Setting SIM switch to USIM (0)");
        } else {
            Logger.w(TAG, "Unknown SIM type: " + sim + ", ignoring");
            addLog("Unknown SIM type: " + sim + ", ignoring");
            return;
        }
        addLog("SIM switch successful");
    }

    /**
     * Write to file.
     */
    private void writeToFile(String path, String value) throws IOException {
        File file = new File(path);
        if (!file.exists() || !file.canWrite()) {
            throw new IOException("Cannot write to file: " + path);
        }

        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            fos.write(value.getBytes());
            fos.flush();
            Logger.i(TAG, "Successfully wrote '" + value + "' to " + path);
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException ioEx) {
                    Logger.e(TAG, "Error closing file: " + ioEx.getMessage(), ioEx);
                }
            }
        }
    }

    /**
     * Test app monitoring functionality
     */
    private void testAppMonitor() {
        Logger.i(TAG, "Testing app monitoring functionality");

        if (!mAppMonitorBound || mAppMonitorService == null) {
            Logger.e(TAG, "App monitoring service not bound, cannot test");
            Toast.makeText(this, "App monitoring service not bound, please try again later", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get monitoring configuration and status
        boolean enabled = mAppMonitorService.isEnabled();
        int checkInterval = mAppMonitorService.getCheckIntervalSeconds();
        List<String> monitorPackages = mAppMonitorService.getMonitorPackages();
        Map<String, Integer> restartCounts = mAppMonitorService.getRestartCountMap();

        // Build status information
        StringBuilder infoBuilder = new StringBuilder();
        infoBuilder.append("App monitoring status:\n");
        infoBuilder.append("- Enabled status: ").append(enabled ? "Enabled" : "Disabled").append("\n");
        infoBuilder.append("- Check interval: ").append(checkInterval).append(" seconds\n");
        infoBuilder.append("- Monitored apps: ").append(monitorPackages.size()).append(" apps\n");

        if (monitorPackages.isEmpty()) {
            infoBuilder.append("  (No monitored apps)\n");
        } else {
            for (int i = 0; i < monitorPackages.size(); i++) {
                String pkg = monitorPackages.get(i);
                int restarts = restartCounts.getOrDefault(pkg, 0);
                infoBuilder.append("  ").append(i+1).append(". ").append(pkg)
                        .append(" (Restart count: ").append(restarts).append(")\n");
            }
        }

        // Display test results
        Logger.i(TAG, infoBuilder.toString());

        // Prompt and force check
        String toastMsg = "App monitoring status refreshed. Found " + monitorPackages.size() + " monitored apps.";
        Toast.makeText(this, toastMsg, Toast.LENGTH_LONG).show();

        // Trigger a check
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // Delay execution by one second to avoid UI lag
                    Thread.sleep(THREAD_DELAY_MS);

                    // Trigger a check by reloading configuration
                    Intent intent = new Intent(MainActivity.this, MonitorAppService.class);
                    intent.putExtra("reload_config", true);
                    startService(intent);

                    Logger.i(TAG, "App monitoring check triggered");
                } catch (Exception ex) {
                    Logger.e(TAG, "Failed to trigger check: " + ex.getMessage());
                }
            }
        }).start();
    }

    /**
     * Check AppOps permission status.
     */
    private void checkAppOpsPermissions(String packageName) {
        addLog("=== Checking AppOps Permissions ===");

        try {
            android.app.AppOpsManager appOps = (android.app.AppOpsManager) getSystemService(Context.APP_OPS_SERVICE);
            if (appOps == null) {
                addLog("❌ AppOpsManager not available");
                return;
            }

            PackageManager pm = getPackageManager();
            android.content.pm.ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
            int uid = appInfo.uid;

            // Check common AppOps permissions
            String[] appOpsPermissions = {
                "MANAGE_EXTERNAL_STORAGE",
                "SYSTEM_ALERT_WINDOW",
                "WRITE_SETTINGS",
                "REQUEST_INSTALL_PACKAGES",
                "ACCESS_NOTIFICATIONS"
            };

            String[] appOpsStrings = {
                android.app.AppOpsManager.OPSTR_MANAGE_EXTERNAL_STORAGE,
                android.app.AppOpsManager.OPSTR_SYSTEM_ALERT_WINDOW,
                android.app.AppOpsManager.OPSTR_WRITE_SETTINGS,
                android.app.AppOpsManager.OPSTR_REQUEST_INSTALL_PACKAGES,
                android.app.AppOpsManager.OPSTR_ACCESS_NOTIFICATIONS
            };

            int allowedCount = 0;
            for (int i = 0; i < appOpsPermissions.length; i++) {
                try {
                    int mode = appOps.checkOpNoThrow(appOpsStrings[i], uid, packageName);
                    boolean isAllowed = (mode == android.app.AppOpsManager.MODE_ALLOWED);
                    String status = isAllowed ? "✓ ALLOWED" : "✗ DENIED";

                    addLog(appOpsPermissions[i] + ": " + status + " (mode: " + mode + ")");

                    if (isAllowed) {
                        allowedCount++;
                    }
                } catch (Exception ex) {
                    addLog(appOpsPermissions[i] + ": ❌ ERROR - " + ex.getMessage());
                }
            }

            addLog("AppOps Summary: " + allowedCount + "/" + appOpsPermissions.length + " permissions allowed");

        } catch (PackageManager.NameNotFoundException nameEx) {
            addLog("❌ Package not found: " + packageName);
        } catch (Exception ex) {
            addLog("❌ Error checking AppOps permissions: " + ex.getMessage());
            Logger.e(TAG, "Error checking AppOps permissions", ex);
        }

        addLog("===========================================");
    }

    /**
     * Check AppInfo configuration status.
     */
    private void checkAppInfoStatus() {
        addLog("=== Checking AppInfo Configuration Status ===");

        try {
            if (mBound && mService != null) {
                // Check AppInfo status through SettingsManagerService
                addLog("SettingsManagerService is available");
                addLog("Checking AppInfo configuration...");

                // Here you can call SettingsManagerService methods to check AppInfo status
                // For example, check if configuration file exists, if TestApp is in configuration, etc.

                Toast.makeText(this, "AppInfo status check completed", Toast.LENGTH_SHORT).show();
            } else {
                addLog("❌ SettingsManagerService not available");
                Toast.makeText(this, "SettingsManagerService not available", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception ex) {
            Logger.e(TAG, "Error checking AppInfo status: " + ex.getMessage(), ex);
            addLog("❌ Error checking AppInfo status: " + ex.getMessage());
            Toast.makeText(this, "Error checking AppInfo status", Toast.LENGTH_SHORT).show();
        }

        addLog("===========================================");
    }

    /**
     * Get maximum log length
     * @return Maximum log length
     */
    private static int getMaxLogLength() {
        return MAX_LOG_LENGTH;
    }
}
