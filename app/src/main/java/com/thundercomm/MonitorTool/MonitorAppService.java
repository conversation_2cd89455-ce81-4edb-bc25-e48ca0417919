/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.MonitorTool;

import android.app.ActivityManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.thundercomm.MonitorTool.utils.ConfigKeys;
import com.thundercomm.MonitorTool.utils.ConfigToolServiceHelper;
import com.thundercomm.MonitorTool.utils.Logger;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * App monitoring service
 * Monitor specified apps and restart them if not running
 */
public class MonitorAppService extends Service {
    private static final String TAG = "MonitorAppService";

    // Constants
    private static final int DEFAULT_CHECK_INTERVAL_SECONDS = 10;
    private static final String INTENT_EXTRA_RELOAD_CONFIG = "reload_config";
    private static final String INTENT_EXTRA_RECONNECT_SERVICE = "reconnect_service";
    private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final long RETRY_DELAY_MS = 3000; // 3 seconds retry interval

    // Current configuration
    private boolean mEnabled = true;
    private int mCheckIntervalSeconds = DEFAULT_CHECK_INTERVAL_SECONDS; // Default 10 seconds for testing
    private List<String> mMonitorPackages = new ArrayList<>();
    private boolean mLogAppRestart = true;

    // Record app restart counts
    private Map<String, Integer> mRestartCountMap = new HashMap<>();
    // Record last restart time
    private Map<String, Long> mLastRestartTimeMap = new HashMap<>();

    // Scheduler
    private ScheduledExecutorService mScheduler;
    private Handler mMainHandler;

    // ConfigToolServiceHelper instance
    private ConfigToolServiceHelper mConfigHelper;

    // Binder instance
    private final IBinder mBinder = new LocalBinder();

    /**
     * Local Binder class for client binding
     */
    public class LocalBinder extends Binder {
        public MonitorAppService getService() {
            return MonitorAppService.this;
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.i(TAG, "MonitorAppService onCreate - Service created");

        mMainHandler = new Handler(Looper.getMainLooper());

        // Initialize ConfigToolServiceHelper
        initConfigToolHelper();

        Logger.i(TAG, "App monitoring service initialization completed");
    }

    /**
     * Initialize ConfigToolServiceHelper
     */
    private void initConfigToolHelper() {
        mConfigHelper = ConfigToolServiceHelper.getInstance();

        // Set service listener
        mConfigHelper.addListener(new ConfigToolServiceHelper.ConfigToolServiceEnhancedListenerAdapter() {
            @Override
            public void onServiceConnected() {
                Logger.i(TAG, "ConfigTool service connected, starting to load configuration");
                mMainHandler.post(() -> loadConfigWithRetry(getMaxConfigRetries(), RETRY_DELAY_MS));
            }

            @Override
            public void onServiceDisconnected() {
                Logger.w(TAG, "ConfigTool service disconnected");
            }

            @Override
            public void onServiceBindFailed() {
                Logger.e(TAG, "ConfigTool service bind failed, using default configuration");
                // When service binding fails, use default empty list, don't start monitoring
                mMonitorPackages = new ArrayList<>();
                mEnabled = false;
                startScheduler(); // Even though disabled, start scheduler for possible service recovery
            }

            @Override
            public void onConfigValuesChanged(Map<String, Object> changedValues) {
                Logger.i(TAG, "Configuration values updated, checking if reload needed");

                boolean needReload = false;
                boolean configChanged = false;

                // Check if any keys we care about have changed
                for (String key : changedValues.keySet()) {
                    // Full key match
                    if (ConfigKeys.APP_MONITORING_ENABLED.equals(key) ||
                        ConfigKeys.APP_MONITORING_INTERVAL.equals(key) ||
                        ConfigKeys.LOG_APP_RESTART.equals(key)) {
                        needReload = true;
                        break;
                    }

                    // Prefix match, e.g. any keys related to rebootTarget
                    if (key.startsWith(ConfigKeys.MONITOR_APPS)) {
                        needReload = true;
                        break;
                    }
                }

                // If any keys we care about changed, reload configuration
                if (needReload) {
                    Logger.i(TAG, "Detected critical configuration change, reloading configuration");
                    mMainHandler.post(() -> loadConfigWithRetry(getMaxConfigRetries(), RETRY_DELAY_MS));
                } else {
                    Logger.d(TAG, "Changed configuration items not related to app monitoring, no need to reload");
                }
            }
        });

        // Initialize and connect service
        mConfigHelper.init(this);
    }

    /**
     * Load configuration with retry mechanism
     * @param maxRetries Maximum retry count
     * @param retryIntervalMs Retry interval (milliseconds)
     */
    private void loadConfigWithRetry(final int maxRetries, final long retryIntervalMs) {
        // Create retryable Runnable
        class RetryRunnable implements Runnable {
            private int retryCount = 0;
            private boolean essentialConfigLoaded = false;

            @Override
            public void run() {
                try {
                    Logger.i(TAG, "Loading configuration... (Attempt " + (retryCount + 1) + "/" + (maxRetries + 1) + ")");

                    // Try to load configuration
                    boolean configLoaded = loadConfig();

                    // If essential configuration is loaded, mark as loaded and start scheduler
                    if (configLoaded && !essentialConfigLoaded) {
                        essentialConfigLoaded = true;
                        Logger.i(TAG, "Essential configuration loaded successfully, starting service operation");

                        // Update scheduler
                        startScheduler();
                    }

                    // If all configurations are loaded, no need to retry
                    if (configLoaded) {
                        Logger.i(TAG, "All configuration loaded successfully");
                        return;
                    }

                    // If there are still configurations to load and retry attempts left, continue retrying
                    if (retryCount < maxRetries) {
                        retryCount++;
                        Logger.i(TAG, "Will retry loading remaining configurations in " + (retryIntervalMs / 1000) + " seconds...");
                        mMainHandler.postDelayed(this, retryIntervalMs);
                    } else {
                        Logger.e(TAG, "Failed to load all configurations, maximum retries reached");

                        // If essential configuration is not loaded, use default configuration
                        if (!essentialConfigLoaded) {
                            Logger.e(TAG, "Essential configuration not loaded, using default configuration");
                            // When configuration cannot be loaded, use default empty list, don't start monitoring
                            mMonitorPackages = new ArrayList<>();
                            mEnabled = false;
                            startScheduler(); // Even if disabled, start scheduler for possible service recovery
                        }
                    }
                } catch (Exception e) {
                    Logger.e(TAG, "Exception loading configuration: " + e.getMessage(), e);

                    // Check if there are retry attempts left
                    if (retryCount < maxRetries) {
                        retryCount++;
                        Logger.i(TAG, "Will retry loading configuration in " + (retryIntervalMs / 1000) + " seconds...");
                        mMainHandler.postDelayed(this, retryIntervalMs);
                    } else {
                        Logger.e(TAG, "Failed to load configuration, maximum retries reached");

                        // If essential configuration is not loaded, use default configuration
                        if (!essentialConfigLoaded) {
                            // When configuration cannot be loaded, use default empty list, don't start monitoring
                            mMonitorPackages = new ArrayList<>();
                            mEnabled = false;
                            startScheduler(); // Even if disabled, start scheduler for possible service recovery
                        }
                    }
                }
            }
        }

        // Start retry process
        mMainHandler.post(new RetryRunnable());
    }

    /**
     * Load configuration
     * @return true if essential configuration was loaded successfully
     */
    private boolean loadConfig() {
        Logger.i(TAG, "Starting to load app monitoring configuration");

        boolean essentialConfigLoaded = false;
        boolean allConfigLoaded = true;

        // Get whether app monitoring is enabled
        try {
            boolean enabled = mConfigHelper.getBooleanConfig(ConfigKeys.APP_MONITORING_ENABLED, true);
            mEnabled = enabled;
            Logger.i(TAG, "App monitoring enabled: " + mEnabled);
        } catch (Exception e) {
            Logger.e(TAG, "Failed to load app monitoring enabled configuration: " + e.getMessage(), e);
            allConfigLoaded = false;
            // Continue with current value
        }

        // Get check interval
        try {
            int checkIntervalSeconds = mConfigHelper.getIntConfig(ConfigKeys.APP_MONITORING_INTERVAL, DEFAULT_CHECK_INTERVAL_SECONDS);
            mCheckIntervalSeconds = checkIntervalSeconds;
            Logger.i(TAG, "App monitoring check interval: " + mCheckIntervalSeconds + " seconds");
        } catch (Exception e) {
            Logger.e(TAG, "Failed to load app monitoring check interval configuration: " + e.getMessage(), e);
            allConfigLoaded = false;
            // Continue with current value
        }

        // Get whether to log application restart
        try {
            boolean logAppRestart = mConfigHelper.getBooleanConfig(ConfigKeys.LOG_APP_RESTART, true);
            mLogAppRestart = logAppRestart;
            Logger.i(TAG, "Log app restart: " + mLogAppRestart);
        } catch (Exception e) {
            Logger.e(TAG, "Failed to load log app restart configuration: " + e.getMessage(), e);
            allConfigLoaded = false;
            // Continue with current value
        }

        // Get monitored app list from rebootTarget
        List<String> monitorPackages = new ArrayList<>();
        boolean gotRebootTarget = false;

        try {
            List<String> rebootTargets = mConfigHelper.getStringListConfig(ConfigKeys.MONITOR_APPS, new ArrayList<>(), getMaxConfigRetries());
            if (rebootTargets != null && !rebootTargets.isEmpty()) {
                monitorPackages.addAll(rebootTargets);
                gotRebootTarget = true;
                essentialConfigLoaded = true; // This is the critical configuration for MonitorAppService
                Logger.i(TAG, "Successfully got " + rebootTargets.size() + " targets from rebootTarget configuration");
            } else {
                Logger.w(TAG, "Failed to get rebootTarget configuration or it was empty");
                allConfigLoaded = false;
            }
        } catch (Exception e) {
            Logger.e(TAG, "Error getting rebootTarget configuration: " + e.getMessage(), e);
            allConfigLoaded = false;
        }



        // Check if monitored list was obtained
        if (monitorPackages.isEmpty() && !gotRebootTarget) {
            Logger.w(TAG, "No valid monitored app list obtained, disabling app monitoring feature");
            mEnabled = false;
        } else {
            // After successfully obtaining the monitored list, automatically enable app monitoring
            mEnabled = true;
            Logger.i(TAG, "Successfully obtained monitored app list, automatically enabling app monitoring feature");
        }

        // Check if monitored app list has changed
        if (!monitorPackages.equals(mMonitorPackages)) {
            Logger.i(TAG, "Monitored app list changed: " + mMonitorPackages.size() + " apps -> " + monitorPackages.size() + " apps");

            // Output new list details
            StringBuilder sb = new StringBuilder("New monitored app list:\n");
            for (int i = 0; i < monitorPackages.size(); i++) {
                String pkgName = monitorPackages.get(i);
                sb.append(i + 1).append(". ").append(pkgName).append("\n");
            }
            Logger.i(TAG, sb.toString());

            // Update list
            mMonitorPackages = new ArrayList<>(monitorPackages);
        }

        return essentialConfigLoaded;
    }

    /**
     * Get maximum retries for config loading
     * @return Maximum retries
     */
    private static int getMaxConfigRetries() {
        return 5;
    }

    /**
     * Get restart count map
     */
    public Map<String, Integer> getRestartCountMap() {
        return new HashMap<>(mRestartCountMap);
    }

    /**
     * Get last restart time map
     */
    public Map<String, Long> getLastRestartTimeMap() {
        return new HashMap<>(mLastRestartTimeMap);
    }

    /**
     * Get monitored package list
     */
    @NonNull
    public List<String> getMonitorPackages() {
        return new ArrayList<>(mMonitorPackages);
    }

    /**
     * Get check interval (seconds)
     */
    public int getCheckIntervalSeconds() {
        return mCheckIntervalSeconds;
    }

    /**
     * Get enabled status
     */
    public boolean isEnabled() {
        return mEnabled;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.i(TAG, "MonitorAppService onStartCommand - Service start command received, startId=" + startId);

        // If there is intent data, it can be processed
        if (intent != null) {
            if (intent.hasExtra(INTENT_EXTRA_RELOAD_CONFIG)) {
                Logger.i(TAG, "Received request to reload configuration");
                loadConfigWithRetry(getMaxConfigRetries(), RETRY_DELAY_MS);
            }

            if (intent.hasExtra(INTENT_EXTRA_RECONNECT_SERVICE) && !mConfigHelper.isServiceConnected()) {
                Logger.i(TAG, "Received request to reconnect service");
                mConfigHelper.init(this);
            }
        }

        return START_STICKY; // Service is automatically restarted after being killed
    }

    @Override
    public void onDestroy() {
        Logger.i(TAG, "MonitorAppService onDestroy - Service destroyed");

        // Unbind ConfigTool service
        if (mConfigHelper != null) {
            mConfigHelper.removeListener(null); // Remove all listeners
            mConfigHelper.unbindService(this);
        }

        // Stop scheduler
        if (mScheduler != null && !mScheduler.isShutdown()) {
            mScheduler.shutdown();
            mScheduler = null;
        }

        super.onDestroy();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Logger.d(TAG, "MonitorAppService onBind - Service bound: " + intent);
        return mBinder;
    }

    /**
     * Start scheduler to periodically check app status
     */
    private void startScheduler() {
        if (mScheduler != null && !mScheduler.isShutdown()) {
            Logger.d(TAG, "Closing old scheduler");
            mScheduler.shutdown();
        }

        if (!mEnabled) {
            Logger.i(TAG, "App monitoring feature is not enabled, do not start scheduler");
            return;
        }

        Logger.d(TAG, "Creating new scheduler, check interval: " + mCheckIntervalSeconds + " seconds");
        mScheduler = Executors.newSingleThreadScheduledExecutor();
        mScheduler.scheduleAtFixedRate(this::checkApps, 0, mCheckIntervalSeconds, TimeUnit.SECONDS);

        Logger.i(TAG, "App monitoring scheduler started, check interval: " + mCheckIntervalSeconds + " seconds, monitoring " + mMonitorPackages.size() + " apps");
    }

    /**
     * Check if all monitored apps are running
     */
    private void checkApps() {
        if (!mEnabled || mMonitorPackages.isEmpty()) {
            Logger.d(TAG, "App monitoring is not enabled or no apps to monitor");
            return;
        }

        Logger.d(TAG, "Starting to check status of " + mMonitorPackages.size() + " apps");

        for (String packageName : mMonitorPackages) {
            boolean isRunning = isAppRunning(packageName);
            Logger.v(TAG, "App[" + packageName + "] running status: " + (isRunning ? "Running" : "Not running"));

            if (!isRunning) {
                Logger.w(TAG, "App[" + packageName + "] not running, attempting to start");
                startApp(packageName);
            }
        }
    }

    /**
     * Check if an app is running
     * @param packageName App package name
     * @return Whether it is running
     */
    private boolean isAppRunning(String packageName) {
        try {
            ActivityManager am = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningAppProcessInfo> processes = am.getRunningAppProcesses();
            boolean isRunning = false;

            if (processes != null) {
                for (ActivityManager.RunningAppProcessInfo process : processes) {
                    if (process.processName.equals(packageName)) {
                        Logger.d(TAG, "Detected app[" + packageName + "] running via RunningAppProcesses");
                        isRunning = true;
                        break;
                    }
                }
            }

            if (!isRunning) {
                Logger.v(TAG, "App[" + packageName + "] not detected running");
            }

            return isRunning;
        } catch (Exception e) {
            Logger.e(TAG, "Exception checking app running status: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * Start app
     * @param packageName App package name
     */
    private void startApp(String packageName) {
        try {
            // Record restart count
            int restartCount = mRestartCountMap.getOrDefault(packageName, 0) + 1;
            mRestartCountMap.put(packageName, restartCount);

            // Record restart time
            long now = System.currentTimeMillis();
            mLastRestartTimeMap.put(packageName, now);

            // Get package manager
            PackageManager pm = getPackageManager();

            // Get launch intent
            Intent launchIntent = pm.getLaunchIntentForPackage(packageName);

            if (launchIntent != null) {
                // Add flag to ensure it starts in a new task
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

                // Start app
                startActivity(launchIntent);

                // Log
                String appLabel;
                try {
                    ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                    appLabel = pm.getApplicationLabel(appInfo).toString();
                } catch (Exception e) {
                    appLabel = packageName;
                }

                SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_PATTERN, Locale.getDefault());
                String timeStr = sdf.format(new Date(now));

                Logger.i(TAG, "Successfully restarted app: " + appLabel + " (" + packageName + "), restart count: " + restartCount + ", time: " + timeStr);

                if (mLogAppRestart) {
                    // Can save restart logs to file or database
                    // Simplified implementation here, just output logs
                }
            } else {
                Logger.e(TAG, "Could not get launch intent for app[" + packageName + "], it might not be a launchable app");
            }
        } catch (Exception e) {
            Logger.e(TAG, "Failed to start app[" + packageName + "]: " + e.getMessage(), e);
        }
    }
}