<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="6dp">

        <!-- Title -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="6dp" />

        <!-- Storage Status -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#EEEEEE"
            android:padding="6dp"
            android:layout_marginBottom="6dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Storage Status"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="2dp" />

            <TextView
                android:id="@+id/statusText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Loading..."
                android:textSize="12sp" />
        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="6dp">

            <Button
                android:id="@+id/checkButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Check Storage"
                android:textSize="10sp"
                android:layout_marginEnd="4dp"
                android:padding="0dp"
                android:minHeight="0dp" />

            <Button
                android:id="@+id/createFileButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Create Test File"
                android:textSize="10sp"
                android:padding="0dp"
                android:minHeight="0dp" />
        </LinearLayout>
        
        <!-- Config Test Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="6dp">

            <Button
                android:id="@+id/testConfigButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Test ConfigTool Config"
                android:textSize="10sp"
                android:padding="0dp"
                android:layout_marginEnd="4dp"
                android:minHeight="0dp" />

            <Button
                android:id="@+id/testAppMonitorButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Test App Monitoring"
                android:textSize="10sp"
                android:padding="0dp"
                android:minHeight="0dp" />
        </LinearLayout>

        <!-- SIM Switch Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="6dp">

            <Button
                android:id="@+id/switchToEsimButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Switch to eSIM"
                android:textSize="10sp"
                android:padding="0dp"
                android:layout_marginEnd="4dp"
                android:minHeight="0dp" />

            <Button
                android:id="@+id/switchToUsimButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Switch to USIM"
                android:textSize="10sp"
                android:padding="0dp"
                android:minHeight="0dp" />
        </LinearLayout>

        <!-- SIM Test Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="6dp">

            <Button
                android:id="@+id/testSimEsimButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Test eSIM"
                android:textSize="10sp"
                android:padding="0dp"
                android:layout_marginEnd="4dp"
                android:minHeight="0dp" />

            <Button
                android:id="@+id/testSimUsimButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Test USIM"
                android:textSize="10sp"
                android:padding="0dp"
                android:layout_marginEnd="4dp"
                android:minHeight="0dp" />

            <Button
                android:id="@+id/testSimStatusButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Check SIM Status"
                android:textSize="10sp"
                android:padding="0dp"
                android:minHeight="0dp" />
        </LinearLayout>

        <!-- AppInfo Test Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="6dp">

            <Button
                android:id="@+id/testAppInfoPermissionsButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Test Permissions"
                android:textSize="10sp"
                android:padding="0dp"
                android:layout_marginEnd="4dp"
                android:minHeight="0dp" />

            <Button
                android:id="@+id/testAppInfoDozeButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Test Doze Whitelist"
                android:textSize="10sp"
                android:padding="0dp"
                android:minHeight="0dp" />
        </LinearLayout>

        <!-- Doze Whitelist Test Button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="6dp">

            <Button
                android:id="@+id/checkDozeWhitelistButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Check Doze Whitelist"
                android:textSize="10sp"
                android:padding="0dp"
                android:layout_marginEnd="4dp"
                android:minHeight="0dp" />

            <Button
                android:id="@+id/checkAppInfoStatusButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:text="Check AppInfo Status"
                android:textSize="10sp"
                android:padding="0dp"
                android:minHeight="0dp" />
        </LinearLayout>

        <!-- Log Title and Clear Button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="2dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Running Log"
                android:textSize="14sp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/clearLogButton"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:text="Clear"
                android:textSize="10sp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="2dp" />
        </LinearLayout>

        <!-- Log Area -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="@android:color/darker_gray">

            <ScrollView
                android:id="@+id/logScrollView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="4dp">

                <TextView
                    android:id="@+id/logText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="10sp"
                    android:fontFamily="monospace"
                    android:textColor="@android:color/white" />
            </ScrollView>
        </FrameLayout>

    </LinearLayout>
</ScrollView>
