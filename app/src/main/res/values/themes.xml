<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
-->
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.MonitorTool" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryDark">@color/purple_700</item>
        <item name="colorAccent">@color/teal_200</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:statusBarColor">@color/purple_700</item>
        
        <!-- Small screen adaptation -->
        <item name="android:textSize">12sp</item>
        <item name="buttonStyle">@style/SmallButtonStyle</item>
        <item name="android:buttonStyle">@style/SmallButtonStyle</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowActionBar">false</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <!-- Small button style -->
    <style name="SmallButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:textSize">10sp</item>
        <item name="android:padding">2dp</item>
    </style>
</resources>