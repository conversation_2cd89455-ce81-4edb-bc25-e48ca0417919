<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.thundercomm.MonitorTool"
    android:sharedUserId="android.uid.system">
    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- Android 11+ requires MANAGE_EXTERNAL_STORAGE permission -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <!-- Permission to receive boot broadcast -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!-- Permission to query all app packages -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />

    <!-- Query specific package visibility -->
    <queries>
        <package android:name="com.thundercomm.configtool" />
    </queries>

    <application
        android:allowBackup="true"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:directBootAware="true"
        android:persistent="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/Theme.MonitorTool"
        tools:targetApi="30">
        
        <!-- MainActivity for configuration and testing -->
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <!-- Removed LAUNCHER category to hide from app drawer -->
            </intent-filter>
        </activity>

        <!-- Memory Control Service - one of the main functionalities of the app -->
        <service
            android:name=".MemoryControlService"
            android:enabled="true"
            android:exported="true"
            android:persistent="true">
        </service>

        <!-- App Monitoring Service - monitors app processes and restarts after crashes -->
        <service
            android:name=".MonitorAppService"
            android:enabled="true"
            android:exported="true"
            android:persistent="true">
        </service>

        <!-- Settings Manager Service - manages application settings and configurations -->
        <service
            android:name=".SettingsManagerService"
            android:enabled="true"
            android:exported="true"
            android:persistent="true">
        </service>

        <!-- Boot auto-start receiver -->
        <receiver
            android:name=".BootReceiver"
            android:enabled="true"
            android:exported="true"
            android:priority="1000">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        
        <!-- FileProvider configuration -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>
