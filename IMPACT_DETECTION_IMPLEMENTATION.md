# Impact Detection 功能实现总结

## 功能概述
实现了从config获取`impactDetection`值，并与`Settings.System`中的`collision_enabled`字段进行双向同步的功能。

## 实现的功能
1. **Config → Settings.System**: 当config中的`impactDetection`变化时，自动更新`Settings.System.collision_enabled`
2. **Settings.System → Config**: 当`Settings.System.collision_enabled`变化时，自动调用`saveConfig`更新config
3. **数据转换**: boolean (impactDetection) ↔ int (collision_enabled: true=1, false=0)
4. **循环更新防护**: 防止Config和Settings.System之间的无限循环更新

## 修改的文件

### 1. ConfigKeys.java
- 添加了 `IMPACT_DETECTION = "impactDetection"` 配置键

### 2. ConfigToolServiceHelper.java
- 添加了 `saveConfig(String jsonData)` 方法，用于保存配置到ConfigTool

### 3. SettingsManagerService.java (主要修改)
#### 添加的常量和成员变量:
- `KEY_COLLISION_ENABLED = "collision_enabled"`
- `mCollisionEnabledObserver`: ContentObserver实例
- `mIsUpdatingFromConfig`: 防止循环更新的标志

#### 添加的方法:
- `initCollisionEnabledObserver()`: 初始化ContentObserver
- `handleImpactDetection(boolean)`: 处理impactDetection配置变化
- `saveImpactDetectionToConfig(boolean)`: 保存impactDetection到ConfigTool
- `isImpactDetectionEnabled()`: 静态getter方法

#### 修改的方法:
- `initConfigTypeMap()`: 添加IMPACT_DETECTION配置类型映射
- `onCreate()`: 添加ContentObserver初始化
- `loadAllConfigurations()`: 添加IMPACT_DETECTION到配置加载列表
- `processConfig()`: 添加IMPACT_DETECTION处理分支
- `onDestroy()`: 添加ContentObserver注销

## 工作流程

### 初始化流程:
1. 服务启动 → `onCreate()`
2. 初始化ConfigTool服务 → `initConfigHelper()`
3. 初始化ContentObserver → `initCollisionEnabledObserver()`
4. ConfigTool连接成功 → `loadAllConfigurations()`
5. 加载impactDetection配置 → `processConfig()` → `handleImpactDetection()`

### Config变化流程:
1. Config中impactDetection变化
2. ConfigTool通知 → `onConfigValuesChanged()`
3. 调用 `processConfig()` → `handleImpactDetection()`
4. 设置 `mIsUpdatingFromConfig = true`
5. 更新 `Settings.System.collision_enabled`
6. 重置 `mIsUpdatingFromConfig = false`

### Settings.System变化流程:
1. Settings.System.collision_enabled变化
2. ContentObserver触发 → `onChange()`
3. 检查 `mIsUpdatingFromConfig` (防止循环)
4. 读取新的collision_enabled值
5. 转换为boolean并更新本地缓存
6. 调用 `saveImpactDetectionToConfig()` 保存到ConfigTool

## 使用方法

### 获取当前状态:
```java
boolean isEnabled = SettingsManagerService.isImpactDetectionEnabled();
```

### 手动触发同步:
配置会自动同步，无需手动干预。

## 注意事项
1. 需要确保应用有 `WRITE_SETTINGS` 权限
2. 循环更新防护机制确保不会出现无限循环
3. 所有操作都有完整的异常处理和日志记录
4. 遵循现有代码的设计模式和命名规范

## 测试建议
1. 测试Config变化时Settings.System是否正确更新
2. 测试Settings.System变化时Config是否正确保存
3. 测试循环更新防护是否有效
4. 测试异常情况下的错误处理
