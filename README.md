# MonitorTool Settings Management

## SettingsManagerService Implementation

### Overview
The `SettingsManagerService` provides a centralized way to manage configuration settings in the MonitorTool application. It interfaces with the `ConfigToolServiceHelper` to retrieve, store, and monitor configuration changes.

### Design Approach
After considering different implementation options, we chose a simple and maintainable approach that:
1. Uses a static configuration map to store settings
2. Provides type-specific handling for different configuration values (String, Boolean, JSON, etc.)
3. Broadcasts configuration changes to notify other components
4. Offers static getter methods for easy access to configurations

### Key Features
- Centralized configuration management
- Type-safe configuration access
- Change notification via broadcasts
- Easy extensibility for new configuration parameters

### Configuration Types
The service handles multiple configuration types:
- String values
- Boolean flags
- JSON data (parsed as needed)
- Numeric values (int, float)

### Adding New Configurations
To add a new configuration parameter, only three modifications are needed:
1. Add a configuration key constant and its type in `ConfigKeys`
2. Add the key to the list of configurations to load in `SettingsManagerService`
3. Add handling logic in the `processConfig` method based on the parameter type

### Current Configurations
- `sim`: SIM card type configuration (esim/usim) with GPIO control
- `whitelistApps`: List of whitelisted application packages
- `agps`: AGPS status configuration

### Implementation Notes
- The service initializes configurations on startup
- It registers listeners with `ConfigToolServiceHelper` to receive updates
- Configuration changes trigger broadcasts to notify other components
- Static getter methods provide easy access to configuration values
- Configuration keys are defined in the `ConfigKeys` class
- Functionality-specific logic is implemented in the respective service classes

### SIM Card Switching
The service supports automatic SIM card switching based on configuration:
- When "sim" configuration is set to "esim", it writes "1" to the GPIO control file
- When "sim" configuration is set to "usim", it writes "0" to the GPIO control file
- The GPIO control file path is: `/sys/devices/platform/soc/soc:gpio_info/sim_switch_gpio122`

### Usage Example
```java
// Get a configuration value
String simConfig = SettingsManagerService.getSimConfig();

// Check if an app is whitelisted
boolean isWhitelisted = SettingsManagerService.isAppWhitelisted("com.example.app");

// Get AGPS status
boolean agpsEnabled = SettingsManagerService.isAgpsEnabled();
``` 