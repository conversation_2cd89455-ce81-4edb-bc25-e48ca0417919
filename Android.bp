java_import {
    name: "gson-prebuilt_monitor",
    jars: ["app/libs/gson-2.10.1.jar"],
    sdk_version: "current",
}

java_import {
    name: "configtool.api",
    jars: ["app/libs/configtool.api.jar"],
    sdk_version: "current",
}

// Then in your android_app block, change libs to module name:
android_app {
    name: "MonitorTool",
    platform_apis: true,
    privileged: true,
    certificate: "platform",


    required: ["privapp_allowlist_com.thundercomm.MonitorTool"],
    srcs: [
        "app/src/main/java/**/*.java",
    ],
    resource_dirs: ["app/src/main/res"],
    manifest: "app/src/main/AndroidManifest.xml",

    static_libs: [
        "androidx.appcompat_appcompat",
        "androidx.annotation_annotation",
        "com.google.android.material_material",
        "androidx-constraintlayout_constraintlayout",
        "gson-prebuilt_monitor",
        "configtool.api",
    ],

    // sdk_version: "current",
    min_sdk_version: "30",

    optimize: {
        enabled: false,
    },

    system_ext_specific: false,
}

prebuilt_etc {
    name: "privapp_allowlist_com.thundercomm.MonitorTool",
    src: "privapp-permissions-com.thundercomm.MonitorTool.xml",
    sub_dir: "permissions",
    filename_from_src: true,
}
